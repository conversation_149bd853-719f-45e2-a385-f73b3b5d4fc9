<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Plugin to manage offers
 *
 * @package    local_offermanager
 * @copyright  2023 Sebrae
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->libdir . '/adminlib.php');
require_login();

use core\context\system as context_system;

require_capability(
    'local/offermanager:manage',
    context_system::instance(),
    null,
    true,
    'error:accessdenied',
    'local_offermanager'
);

if(!get_config('local_offermanager', 'enableplugin')){
    redirect(
        new moodle_url('/'),
        get_string('error:accessdenied', 'local_offermanager'),
        null,
        \core\output\notification::NOTIFY_ERROR
    );
}

// Detectar a rota atual a partir da URL
$route = optional_param('route', '', PARAM_TEXT);
$url = new moodle_url('/local/offermanager/');

// Verificar se estamos em uma URL de edição de oferta ou turma
$current_url = $_SERVER['REQUEST_URI'];
$edit_offer_pattern = '/\/local\/offermanager\/edit-offer\/(\d+)/';
$new_subscribed_users_pattern = '/\/local\/offermanager\/new-subscribed-users\/(\d+)/';
$edit_class_pattern = '/\/local\/offermanager\/edit-class\/(\d+)\/(\d+)\/(\d+)/';
$new_class_pattern = '/\/local\/offermanager\/new-class\/(\d+)\/(\d+)/';
$offer_id = null;
$subscribe_id = null;
$offercourseid = null;
$classid = null;
$class_offerid = null;

if (preg_match($edit_offer_pattern, $current_url, $matches)) {
    $route = 'edit-offer';
    $offer_id = $matches[1];
} else if (preg_match($new_subscribed_users_pattern, $current_url, $matches)) {
    $route = 'new-subscribed-users';
    $subscribe_id = $matches[1];
} else if (preg_match($edit_class_pattern, $current_url, $matches)) {
    $route = 'edit-class';
    $offercourseid = $matches[1];
    $classid = $matches[2];
    $class_offerid = $matches[3];
} else if (preg_match($new_class_pattern, $current_url, $matches)) {
    $route = 'new-class';
    $offercourseid = $matches[1];
    $class_offerid = $matches[2];
}

// Definir título e navegação com base na rota
$title = 'Gerenciar Ofertas';
$showNewOfferNav = false;
$showNewClassNav = false;
$showEditClassNav = false;

if ($route === 'new-offer') {
    $title = 'Nova Oferta';
    $showNewOfferNav = true;
} else if ($route === 'edit-offer') {
    $title = 'Editar Oferta';
    $showNewOfferNav = false;
} else if ($route === 'new-subscribed-users') {
    $title = 'Usuários matriculados';
    $showNewOfferNav = false;
} else if ($route === 'new-class') {
    $title = 'Nova Turma';
    $showNewClassNav = true;
} else if ($route === 'edit-class') {
    $title = 'Editar Turma';
    $showEditClassNav = true;
}

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_pagetype('local-offermanager');
$PAGE->set_pagelayout('standard');
$PAGE->set_title($title);
$PAGE->set_heading($title);
$PAGE->set_url(new moodle_url($url, ['route' => $route]));
$PAGE->navbar->add('Gerenciador de Ofertas', new moodle_url($url));

if ($showNewOfferNav) {
    $PAGE->navbar->add('Nova Oferta', new moodle_url($url, ['route' => 'new-offer']));
} else if ($route === 'edit-offer' && $offer_id) {
    $PAGE->navbar->add('Editar Oferta', new moodle_url($url, ['route' => 'edit-offer']));
} else if ($route === 'new-subscribed-users' && $subscribe_id) {
    $PAGE->navbar->add('Usuários matriculados', new moodle_url($url, ['route' => 'new-subscribed-users']));
} else if ($route === 'new-class' && $offercourseid) {
    $PAGE->navbar->add('Nova Turma', new moodle_url($url, ['route' => 'new-class']));
} else if ($route === 'edit-class' && $offercourseid && $classid) {
    $PAGE->navbar->add('Editar Turma', new moodle_url($url, ['route' => 'edit-class']));
}

// Incluir o arquivo CSS gerado pelo build
$PAGE->requires->css(new moodle_url('/local/offermanager/amd/build/app/style.css'));

// Passar a rota atual para o JavaScript
$options = ['route' => $route];
if ($route === 'edit-offer' && $offer_id) {
    $options['offerId'] = $offer_id;
}

if ($route === 'new-subscribed-users' && $subscribe_id) {
    $options['subscribeId'] = $subscribe_id;
}

if ($route === 'new-class' && $offercourseid) {
    $options['offercourseid'] = $offercourseid;
    if ($class_offerid) {
        $options['offerid'] = $class_offerid;
    }
}

if ($route === 'edit-class' && $offercourseid && $classid) {
    $options['offercourseid'] = $offercourseid;
    $options['classid'] = $classid;
    if ($class_offerid) {
        $options['offerid'] = $class_offerid;
    }
}

$PAGE->requires->js_call_amd('local_offermanager/app/app-lazy', 'init', [
    '#root-offermanager',
    $options
]);

echo $OUTPUT->header();

echo html_writer::div('', '', ['id' => 'root-offermanager']);

echo $OUTPUT->footer();