<template>
  <div id="offer-manager-component" class="offer-manager">
    <PageHeader title="Gerenciar Ofertas">
      <template #actions>
        <div class="new-offer-container">
          <CustomButton
            variant="primary"
            label="Nova Oferta"
            @click="createNewOffer"
          />
        </div>
      </template>
    </PageHeader>

    <!-- Filtros -->
    <FilterSection title="FILTRO">
      <FilterRow :inline="true">
        <FilterGroup label="Oferta">
          <CustomInput
            v-model="inputFilters.search"
            placeholder="Buscar..."
            :width="339"
            :has-search-icon="true"
            @input="debouncedSearch"
          />
        </FilterGroup>

        <FilterGroup label="Tipo">
          <CustomSelect
            v-model="inputFilters.type"
            :options="typeSelectOptions"
            :width="144"
            @update:modelValue="handleTypeChange"
          />
        </FilterGroup>

        <FilterGroup :is-checkbox="true">
          <CustomCheckbox
            v-model="inputFilters.hideInactive"
            id="hideInactive"
            label="Não exibir inativas"
            @update:modelValue="handleHideInactiveChange"
          />
        </FilterGroup>

        <FilterActions>
          <CustomButton
            variant="secondary"
            label="Limpar"
            @click="clearFilters"
          />
        </FilterActions>
      </FilterRow>
    </FilterSection>

    <!-- Mensagem de Erro -->
    <div class="alert alert-danger" v-if="error">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <!-- Tabela -->
    <div class="table-container">
      <CustomTable
        :headers="tableHeaders"
        :items="offers"
        :sort-by="sortBy"
        :sort-desc="sortDesc"
        @sort="handleTableSort"
      >
        <template #item-description="{ item }">
          <span :title="item.description">
            {{
              item.description.length > 50
                ? item.description.slice(0, 50) + "..."
                : item.description
            }}
          </span>
        </template>
        <template #item-type="{ item }">
          {{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }}
        </template>
        <template #item-status="{ item }">
          {{ item.status === 1 ? "Ativa" : "Inativa" }}
        </template>
        <template #item-actions="{ item }">
          <div class="action-buttons">
            <button
              class="btn-action btn-edit"
              @click="editOffer(item)"
              title="Editar"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_9_197955)">
                  <path
                    d="M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z"
                    fill="var(--primary)"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_9_197955">
                    <rect width="16" height="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </button>
            <button
              class="btn-action"
              :class="item.status === 1 ? 'btn-deactivate' : 'btn-activate'"
              @click="toggleOfferStatus(item)"
              :disabled="item.status === 0 && !item.can_activate"
              :title="getStatusButtonTitle(item)"
            >
              <template v-if="item.status === 1">
                <i class="fas fa-eye"></i>
              </template>
              <template v-else>
                <i class="fas fa-eye-slash"></i>
              </template>
            </button>
            <button
              class="btn-action btn-delete"
              @click="deleteOffer(item)"
              :disabled="!item.can_delete"
              :title="
                item.can_delete
                  ? 'Excluir'
                  : 'Não é possível excluir esta oferta'
              "
            >
              <i class="fa fa-trash fa-fw"></i>
            </button>
          </div>
        </template>
      </CustomTable>
    </div>

    <!-- Paginação -->
    <Pagination
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="totalOffers"
      :loading="loading"
    />

    <!-- Modal de Confirmação de Exclusão -->
    <ConfirmationModal
      :show="showDeleteModal"
      title="A exclusão desta instância de oferta é uma ação irreversível."
      message="Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir Oferta"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteModal = false"
      @confirm="confirmDeleteOffer"
    />

    <!-- Modal de Confirmação de Alteração de Status -->
    <ConfirmationModal
      :show="showStatusModal"
      :title="
        selectedOffer?.status === 1
          ? 'Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedOffer?.status === 1
          ? ''
          : 'Tem certeza que deseja ativar esta oferta?'
      "
      :list-title="
        selectedOffer?.status === 1
          ? 'Comportamento para os cursos, turmas e matrículas:'
          : ''
      "
      :list-items="
        selectedOffer?.status === 1
          ? [
              'Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.',
              'Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.',
              'Novos alunos não poderão ser inscritos através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedOffer?.status === 1 ? 'Inativar oferta' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedOffer?.status === 1 ? 'warning' : 'question'"
      @close="showStatusModal = false"
      @confirm="confirmToggleStatus"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import pencilFill from "@/assets/img/pencil-fill.svg";
import { debounce } from "lodash";

// Importação dos services
import {
  fetchOffers,
  deleteOffer,
  toggleOfferStatus,
  getTypeOptions,
} from "@/services/offer";

// Importação dos componentes
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import CustomButton from "@/components/CustomButton.vue";
import FilterSection from "@/components/FilterSection.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterActions from "@/components/FilterActions.vue";
import LFLoading from "@/components/LFLoading.vue";
import Toast from "@/components/Toast.vue";

import Pagination from "@/components/Pagination.vue";
import PageHeader from "@/components/PageHeader.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import { useRouter } from "vue-router";

export default {
  name: "OfferManagerView",

  components: {
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomCheckbox,
    CustomButton,
    FilterSection,
    FilterRow,
    FilterGroup,
    FilterActions,
    Pagination,
    PageHeader,
    ConfirmationModal,
    LFLoading,
    Toast,
  },

  setup() {
    const router = useRouter();
    return { router };
  },

  mounted() {
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css";
      document.head.appendChild(link);
    }
  },

  data() {
    return {
      // Ícones SVG
      icons: {
        edit: pencilFill,
      },

      // Filtros de entrada (não aplicados automaticamente)
      inputFilters: {
        search: "",
        type: "",
        hideInactive: false,
      },

      // Filtros aplicados (usados para filtragem real)
      appliedFilters: {
        search: "",
        type: "",
        hideInactive: false,
      },

      // Opções do select de tipo
      typeOptions: [],

      // Configuração da tabela
      tableHeaders: [
        { text: "NOME DA OFERTA", value: "name", sortable: true },
        { text: "DESCRIÇÃO", value: "description", sortable: true },
        { text: "STATUS DA OFERTA", value: "status", sortable: true },
        { text: "TIPO DA OFERTA", value: "type", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],

      // Estados de carregamento e erro
      offers: [],
      totalOffers: 0,
      loading: false,
      error: null,

      // Toast
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null, // Adicionar referência para o timeout

      // Paginação
      currentPage: 1,
      perPage: 10,

      // Ordenação
      sortBy: "name",
      sortDesc: false,

      // Modal de exclusão
      showDeleteModal: false,
      offerToDelete: null,

      // Modal de alteração de status
      showStatusModal: false,
      selectedOffer: null,
    };
  },

  computed: {
    typeSelectOptions() {
      return [{ value: "", label: "Todos" }, ...this.typeOptions];
    },

    hasActiveFilters() {
      return this.appliedFilters.search || this.appliedFilters.hideInactive;
    },
  },

  watch: {
    perPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentPage = 1;
        this.loadOffers();
      }
    },
    currentPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loadOffers();
      }
    },
  },

  async created() {
    // Criar a versão debounced da função de busca
    this.debouncedSearch = debounce(this.handleSearchInput, 300);

    // Inicializar
    this.loadTypeOptions();
    this.loadOffers();
  },

  methods: {
    getTypeLabel(typeValue) {
      if (!typeValue) return "";

      const option = this.typeOptions.find(
        (opt) => opt.value === typeValue || opt.label === typeValue
      );

      if (option) return option.label;
      return typeValue;
    },

    async loadTypeOptions() {
      try {
        const response = await getTypeOptions();
        if (response?.data?.types) {
          this.typeOptions = response.data.types.map((type) => {
            if (typeof type === "object" && type.value && type.label) {
              return type;
            }
            return {
              value: type,
              label: type.charAt(0).toUpperCase() + type.slice(1),
            };
          });
        }
      } catch (error) {
        this.error = error.message;
      }
    },

    async loadOffers() {
      try {
        this.loading = true;
        this.error = null;

        const params = {
          search: this.appliedFilters.search || "",
          type: this.appliedFilters.type || null,
          onlyActive: this.appliedFilters.hideInactive === true,
          page: this.currentPage,
          perPage: this.perPage,
          sortBy: this.sortBy,
          sortDesc: this.sortDesc,
        };

        const response = await fetchOffers(params);
        const result = Array.isArray(response) ? response[0] : response;

        if (!result.error && result.data) {
          this.offers = result.data.offers || [];
          this.totalOffers = result.data.total_items || 0;
        } else {
          throw new Error(result.message || "Erro ao carregar ofertas");
        }
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async handlePageChange(page) {
      if (page !== this.currentPage) {
        this.currentPage = page;
        await this.loadOffers();
      }
    },

    async handlePerPageChange(newPerPage) {
      if (newPerPage !== this.perPage) {
        this.perPage = newPerPage;
        this.currentPage = 1;
        await this.loadOffers();
      }
    },

    async clearFilters() {
      // Preservar o tipo selecionado
      const currentType = this.inputFilters.type;

      this.inputFilters = {
        search: "",
        type: "",
        hideInactive: false,
      };

      this.appliedFilters = {
        search: "",
        type: "",
        hideInactive: false,
      };

      this.currentPage = 1;
      await this.loadOffers();
    },

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
      this.loadOffers();
    },

    createNewOffer() {
      this.router.push({ name: "nova-oferta" });
    },

    editOffer(offer) {
      this.router.push({
        name: "editar-oferta",
        params: { id: offer.id.toString() },
      });
    },

    deleteOffer(offer) {
      if (!offer.can_delete) {
        return;
      }

      this.offerToDelete = offer;
      this.showDeleteModal = true;
    },

    async confirmDeleteOffer() {
      if (this.offerToDelete) {
        try {
          this.loading = true;
          await deleteOffer(this.offerToDelete.id);
          await this.loadOffers();

          // Exibir mensagem de sucesso
          this.showSuccessMessage(
            `Oferta "${this.offerToDelete.name}" excluída com sucesso`
          );

          this.offerToDelete = null;
          this.showDeleteModal = false;
        } catch (error) {
          this.error = error.message;
          // Exibir mensagem de erro
          this.showErrorMessage(
            `Erro ao excluir oferta "${this.offerToDelete.name}"`
          );
        } finally {
          this.loading = false;
        }
      }
    },

    toggleOfferStatus(offer) {
      if (offer.status === 0 && !offer.can_activate) {
        return;
      }

      this.selectedOffer = offer;
      this.showStatusModal = true;
    },

    async confirmToggleStatus() {
      if (this.selectedOffer) {
        try {
          this.loading = true;
          await toggleOfferStatus(
            this.selectedOffer.id,
            this.selectedOffer.status === 1
          );
          await this.loadOffers();

          // Exibir mensagem de sucesso
          this.showSuccessMessage(
            this.selectedOffer.status === 1
              ? `Oferta "${this.selectedOffer.name}" inativada com sucesso`
              : `Oferta "${this.selectedOffer.name}" inativada com sucesso`
          );

          this.selectedOffer = null;
          this.showStatusModal = false;
        } catch (error) {
          this.error = error.message;
          // Exibir mensagem de erro
          this.showErrorMessage(
            this.selectedOffer.status === 1
              ? `Erro ao inativar oferta "${this.selectedOffer.name}"`
              : `Erro ao ativar oferta "${this.selectedOffer.name}"`
          );
        } finally {
          this.loading = false;
        }
      }
    },

    getStatusButtonTitle(item) {
      if (item.status === 1) {
        return "Desativar";
      } else {
        return item.can_activate
          ? "Ativar"
          : "Não é possível ativar esta oferta";
      }
    },

    // Método para lidar com a mudança de tipo
    async handleTypeChange(value) {
      // Atualiza o filtro aplicado diretamente
      this.appliedFilters.type = value;

      // Reseta para a primeira página
      this.currentPage = 1;

      // Carrega as ofertas com o novo filtro
      await this.loadOffers();
    },

    // Método para lidar com a mudança do checkbox de inativas
    async handleHideInactiveChange(value) {
      // Garantir que value seja um booleano primitivo
      const boolValue = value === true;

      // Sincronizar os valores entre inputFilters e appliedFilters
      this.inputFilters.hideInactive = boolValue;
      this.appliedFilters.hideInactive = boolValue;

      // Reseta para a primeira página
      this.currentPage = 1;

      // Carrega as ofertas com o novo filtro
      await this.loadOffers();
    },

    // Método para lidar com a entrada de texto na pesquisa
    async handleSearchInput() {
      if (
        this.inputFilters.search.length >= 3 ||
        this.inputFilters.search === ""
      ) {
        // Atualiza o filtro aplicado diretamente
        this.appliedFilters.search = this.inputFilters.search;

        // Reseta para a primeira página
        this.currentPage = 1;

        // Carrega as ofertas com o novo filtro
        await this.loadOffers();
      }
    },

    /**
     * Exibe uma mensagem de sucesso usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      // Isso ajuda a reiniciar a animação corretamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000); // Usar a duração definida no componente Toast se necessário
      });
    },

    /**
     * Exibe uma mensagem de erro usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.offer-manager {
  margin-bottom: 2rem;
}

/* Os estilos de .btn-action foram movidos para global.scss */

.btn-activate {
  color: #6c757d;

  &:hover {
    background-color: rgba(108, 117, 125, 0.1);
  }

  i {
    opacity: 0.7;
  }
}

.btn-deactivate {
  color: #fff;
}

.table-container {
  position: relative;
  margin-bottom: 1rem;
  min-height: 400px;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &:not(:empty) {
      opacity: 1;
      visibility: visible;
    }

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2rem;
      padding: 3rem;
      border-radius: 12px;
      background-color: rgba(33, 37, 41, 0.98);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      min-width: 240px;
    }

    .loading-text {
      color: #fff;
      font-size: 1.125rem;
      font-weight: 500;
      letter-spacing: 0.01em;
      text-align: center;
      margin: 0;
      padding: 0;
    }

    .spinner-border {
      width: 3.5rem;
      height: 3.5rem;
      border: 0.3rem solid rgba(255, 255, 255, 0.2);
      border-top-color: #fff;
      border-radius: 50%;
      animation: spin 0.8s ease-in-out infinite;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &.alert-danger {
    background-color: #2c0b0e;
    border: 1px solid #842029;
    color: #ea868f;
  }

  i {
    font-size: 1.25rem;
  }
}
</style>

<style lang="scss">
.table-container {
  table {
    tr {
      th {
        &[data-value="actions"] {
          text-align: center !important;
        }
      }
    }
  }
}
</style>
