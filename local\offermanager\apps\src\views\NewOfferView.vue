<template>
  <div id="new-offer-component" class="new-offer">
    <PageHeader
      :title="isEditing ? `Editar oferta: ${offer.name}` : 'Nova oferta'"
    >
      <template #actions>
        <BackButton @click="goBack" />
      </template>
    </PageHeader>

    <div class="alert alert-warning" v-if="showWarning">
      <i class="fas fa-exclamation-triangle"></i>
      Para que uma instância de oferta seja ativada e disponibilize os cursos
      para os públicos-alvo configurados, é necessário garantir que pelo menos
      um curso, um grupo de público-alvo, e uma turma estejam configurados à
      instância da oferta.
    </div>

    <!-- Configurações Gerais -->
    <div class="section-container">
      <h2 class="section-title">CONFIGURAÇÕES GERAIS</h2>

      <!-- Primeira linha: <PERSON><PERSON> da Oferta, <PERSON><PERSON><PERSON> da Oferta, Público-alvo -->
      <div class="form-row mb-3">
        <div class="form-group">
          <div class="label-container">
            <div class="label-with-help">
              <label class="form-label">Nome da Oferta</label>
              <i
                class="icon fa fa-exclamation-circle text-danger fa-fw"
                title="Obrigatório"
                role="img"
                aria-label="Obrigatório"
              ></i>
            </div>
          </div>
          <div class="input-container">
            <CustomInput
              v-model="offer.name"
              placeholder="Oferta 0001"
              :width="280"
              required
              :has-error="formErrors.name.hasError"
              :error-message="formErrors.name.message"
              @validate="validateField('name')"
            />
          </div>
        </div>

        <div class="form-group" v-if="typeOptionsEnabled">
          <div class="label-container">
            <div class="label-with-help">
              <label class="form-label">Tipo da oferta</label>
            </div>
          </div>
          <div class="input-container">
            <CustomSelect
              v-model="offer.offerType"
              :options="offerTypeOptions"
              :width="280"
            />
          </div>
        </div>
      </div>

      <div class="form-row mb-3" style="margin-bottom: 1.5rem">
        <div class="form-group">
          <div class="label-container">
            <div class="label-with-help">
              <label class="form-label">Público-alvo</label>
              <i
                class="icon fa fa-exclamation-circle text-danger fa-fw"
                title="Obrigatório"
                role="img"
                aria-label="Obrigatório"
              ></i>
              <HelpIcon
                title="Ajuda com público-alvo"
                text="Selecione pelo menos um público-alvo para a oferta."
              />
            </div>
          </div>
          <div class="input-container">
            <Autocomplete
              class="autocomplete-audiences"
              v-model="selectedAudiences"
              :items="audienceTypeOptions"
              placeholder="Pesquisar público-alvo..."
              :input-max-width="218"
              :required="true"
              :show-all-option="true"
              :has-error="formErrors.audiences.hasError"
              :error-message="formErrors.audiences.message"
              @select-all="handleSelectAllAudiences"
              @update:modelValue="validateField('audiences')"
            />
          </div>
        </div>
      </div>

      <!-- Segunda linha: Descrição da oferta -->
      <div class="form-group text-editor-container">
        <div class="label-container">
          <div class="label-with-help">
            <label class="form-label">Descrição da oferta</label>
          </div>
        </div>
        <div class="limited-width-editor">
          <TextEditor
            v-model="offer.description"
            placeholder="Digite a descrição da oferta aqui..."
            :rows="5"
          />
        </div>
      </div>
    </div>

    <!-- Cursos -->
    <div class="section-container" :class="{ 'no-title-section': !isEditing }">
      <!-- Título visível apenas quando está em edição -->
      <h2 v-if="isEditing" class="section-title">CURSOS</h2>

      <!-- Mensagem quando não está em edição ou não pode gerenciar cursos -->
      <div v-if="!canManageCourses || !isEditing" class="message-container">
        <div class="lock-message">
          <i class="fas fa-lock lock-icon"></i>
          <span>Salve a oferta primeiro para gerenciar os cursos</span>
        </div>
      </div>

      <!-- Conteúdo visível apenas quando está em edição -->
      <div v-if="isEditing && canManageCourses">
        <FilterRow inline class="courses-filter-row">
          <div class="filters-left-group">
            <FilterGroup>
              <Autocomplete
                v-model="selectedCategory"
                :items="categoryOptions"
                placeholder="Pesquisar..."
                label="Categoria"
                :input-max-width="218"
                :has-search-icon="true"
                :auto-open="false"
                :show-filter-tags="false"
                :show-selected-in-input="true"
                :no-results-text="
                  categoryOptions.length === 0
                    ? 'Nenhuma categoria disponível'
                    : 'Nenhuma categoria encontrada'
                "
                @select="handleCategorySelect"
              />
            </FilterGroup>

            <FilterGroup>
              <Autocomplete
                v-model="selectedCourse"
                :items="courseOptions"
                placeholder="Pesquisar..."
                label="Curso"
                :input-max-width="218"
                :has-search-icon="true"
                :auto-open="true"
                :loading="loadingCourses || loadingMoreCourses"
                :no-results-text="courseNoResultsText"
                @select="handleCourseSelect"
                @load-more="loadMoreCourses"
                @search="handleCourseSearch"
                ref="courseAutocomplete"
              />
            </FilterGroup>

            <FilterGroup :isCheckbox="true" class="checkbox-filter-group">
              <CustomCheckbox
                v-model="inputFilters.onlyActive"
                id="onlyActive"
                label="Não exibir inativos"
                @change="handleOnlyActiveChange"
              />
            </FilterGroup>

            <!-- Tags de filtro -->
            <FilterTags v-if="appliedFilters.course" class="mt-3">
              <FilterTag @remove="removeFilter('course')">
                Curso: {{ appliedFilters.course }}
              </FilterTag>
            </FilterTags>
          </div>

          <div class="filters-right-group">
            <button class="btn btn-primary" @click="showAddCourseModal">
              Adicionar curso
            </button>
          </div>
        </FilterRow>

        <!-- Tabela de Cursos -->
        <CollapsibleTable
          :headers="courseTableHeaders"
          :items="selectedCourses"
          :sort-by="sortBy"
          :sort-desc="sortDesc"
          @sort="handleTableSort"
          :expandable="true"
        >
          <template #empty-state>
            <div class="empty-state">
              <span class="no-results">{{
                loading ? "Carregando registros..." : "Não existem registros"
              }}</span>
            </div>
          </template>

          <template #item-name="{ item }">
            <span :title="item.name">
              {{
                item.name.length > 50
                  ? item.name.slice(0, 50) + "..."
                  : item.name
              }}
            </span>
          </template>

          <template #item-actions="{ item }">
            <div class="action-buttons">
              <button
                class="btn-action btn-add"
                @click="addTurma(item)"
                title="Adicionar turma"
              >
                <img :src="icons.plus" alt="Adicionar turma" />
              </button>
              <button
                class="btn-action"
                :class="
                  item.status === 'Ativo' ? 'btn-deactivate' : 'btn-activate'
                "
                @click="toggleCourseStatus(item)"
                :disabled="
                  (item.status === 'Inativo' && !item.can_activate) ||
                  !item.can_activate
                "
                :title="getStatusButtonTitle(item)"
              >
                <i
                  :class="
                    item.status === 'Ativo' ? 'fas fa-eye' : 'fas fa-eye-slash'
                  "
                ></i>
              </button>
              <button
                class="btn-action btn-delete"
                @click="deleteCourse(item)"
                :disabled="!item.can_delete"
                :title="
                  item.can_delete
                    ? 'Excluir'
                    : 'Não é possível excluir este curso'
                "
              >
                <i class="fa fa-trash fa-fw"></i>
              </button>
            </div>
          </template>

          <template #expanded-content="{ item }">
            <div class="turmas-container">
              <div class="turmas-header">
                <div class="turma-col">NOME DA TURMA</div>
                <div class="turma-col">TIPO DE INSCRIÇÃO</div>
                <div class="turma-col">QTD. DE VAGAS</div>
                <div class="turma-col">QTD. DE ALUNOS INSCRITOS</div>
                <div class="turma-col">DATA INÍCIO DA TURMA</div>
                <div class="turma-col">DATA FIM DA TURMA</div>
                <div class="turma-col">AÇÕES</div>
              </div>

              <div class="turmas-content">
                <div v-if="item.turmas && item.turmas.length > 0">
                  <div
                    class="turmas-row"
                    v-for="(turma, index) in item.turmas"
                    :key="index"
                  >
                    <div class="turma-col">
                      <span :title="turma.nome">
                        {{
                          turma.nome.length > 20
                            ? turma.nome.slice(0, 20) + "..."
                            : turma.nome
                        }}
                      </span>
                    </div>
                    <div class="turma-col">
                      {{ getEnrolTypeLabel(turma.enrol_type) }}
                    </div>
                    <div class="turma-col">{{ turma.vagas }}</div>
                    <div class="turma-col">{{ turma.inscritos }}</div>
                    <div class="turma-col">{{ turma.dataInicio }}</div>
                    <div class="turma-col">{{ turma.dataFim }}</div>
                    <div class="turma-col">
                      <div class="action-buttons">
                        <button
                          class="btn-action btn-users"
                          @click="viewRegisteredUsers(turma)"
                          title="Usuários Matriculados"
                        >
                          <img :src="icons.users" alt="Usuários Matriculados" />
                        </button>
                        <button
                          class="btn-action btn-edit"
                          @click="editTurma(turma)"
                          title="Editar"
                        >
                          <i class="fas fa-pencil-alt"></i>
                        </button>
                        <button
                          class="btn-action btn-duplicate"
                          @click="duplicateTurma(turma, item)"
                          title="Duplicar Turma"
                        >
                          <i class="fas fa-copy"></i>
                        </button>
                        <button
                          class="btn-action"
                          :class="
                            turma.status === 'Ativo'
                              ? 'btn-deactivate'
                              : 'btn-activate'
                          "
                          :title="
                            turma.status === 'Ativo' ? 'Inativar' : 'Ativar'
                          "
                          @click="toggleClassStatus(turma)"
                        >
                          <i
                            :class="
                              turma.status === 'Ativo'
                                ? 'fas fa-eye'
                                : 'fas fa-eye-slash'
                            "
                          ></i>
                        </button>
                        <button
                          class="btn-action btn-delete"
                          @click="removeTurma(item, index)"
                          :disabled="!turma.can_delete"
                          :title="
                            turma.can_delete
                              ? 'Excluir'
                              : 'Não é possível excluir esta turma'
                          "
                        >
                          <i class="fa fa-trash fa-fw"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-turmas">
                  <span>Nenhuma turma encontrada para este curso</span>
                </div>
              </div>
            </div>
          </template>
        </CollapsibleTable>

        <Pagination
          ref="pagination"
          v-model:current-page="currentPage"
          v-model:per-page="perPage"
          :total="totalItems"
          @update:current-page="handlePageChange"
        />
      </div>
    </div>

    <hr />

    <!-- Ações da Oferta -->
    <div class="d-flex justify-content-between align-items-center">
      <div class="required-fields-message">
        <div class="form-info">
          Este formulário contém campos obrigatórios marcados com
          <i class="fa fa-exclamation-circle text-danger"></i>
        </div>
      </div>
      <div class="actions-container offer-actions">
        <CustomButton variant="primary" label="Salvar" @click="saveOffer" />
        <CustomButton variant="secondary" label="Cancelar" @click="goBack" />
      </div>
    </div>

    <AddCourseModal
      v-model="showAddCourseModalVisible"
      :offer-id="offerId"
      @confirm="handleAddCourseConfirm"
    />

    <!-- Modal de Confirmação de Inativação de Curso -->
    <ConfirmationModal
      :show="showCourseStatusModal"
      :title="
        selectedCourse?.status === 'Ativo'
          ? 'Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedCourse?.status === 'Ativo'
          ? ''
          : 'Tem certeza que deseja ativar este curso?'
      "
      :list-items="
        selectedCourse?.status === 'Ativo'
          ? [
              'O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.',
              'Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.',
              'Novos alunos não poderão ser inscritos através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedCourse?.status === 'Ativo' ? 'Inativar curso' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedCourse?.status === 'Ativo' ? 'warning' : 'question'"
      @close="showCourseStatusModal = false"
      @confirm="confirmToggleCourseStatus"
    />

    <!-- Modal de Confirmação de Exclusão de Curso -->
    <ConfirmationModal
      :show="showDeleteCourseModal"
      title="A exclusão deste curso da instância de oferta é uma ação irreversível"
      message="Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir curso"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteCourseModal = false"
      @confirm="confirmDeleteCourse"
    />

    <!-- Modal de Confirmação de Exclusão de Turma -->
    <ConfirmationModal
      :show="showDeleteClassModal"
      title="A exclusão desta turma é uma ação irreversível"
      message="Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir Turma"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteClassModal = false"
      @confirm="confirmDeleteClass"
    />

    <!-- Modal de Confirmação de Inativação de Turma -->
    <ConfirmationModal
      :show="showClassStatusModal"
      :title="
        selectedClass?.status === 'Ativo'
          ? 'Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedClass?.status === 'Ativo'
          ? ''
          : 'Tem certeza que deseja ativar esta turma?'
      "
      :list-items="
        selectedClass?.status === 'Ativo'
          ? [
              'Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.',
              'Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.',
              'Novos alunos não poderão ser matriculados através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedClass?.status === 'Ativo' ? 'Inativar Turma' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedClass?.status === 'Ativo' ? 'warning' : 'question'"
      @close="showClassStatusModal = false"
      @confirm="confirmToggleClassStatus"
    />

    <!-- Modal de Duplicação de Turma -->
    <DuplicateClassModal
      :show="showDuplicateClassModal"
      :turma="classToDuplicate"
      :parentCourse="classToDuplicateParentCourse"
      :offerId="offerId"
      @close="showDuplicateClassModal = false"
      @success="handleDuplicateSuccess"
      @loading="loading = $event"
      @error="showErrorMessage"
    />

    <!-- Modal de Seleção de Tipo de Inscrição -->
    <EnrolTypeModal
      :show="showEnrolTypeModal"
      :offercourseid="selectedCourseForClass?.offerCourseId"
      :offerid="offerId || '0'"
      @close="showEnrolTypeModal = false"
      @confirm="handleEnrolTypeConfirm"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
// Importação dos componentes
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomButton from "@/components/CustomButton.vue";
import Pagination from "@/components/Pagination.vue";
import CollapsibleTable from "@/components/CollapsibleTable.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import TextEditor from "@/components/TextEditor.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import AddCourseModal from "@/components/AddCourseModal.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import DuplicateClassModal from "@/components/DuplicateClassModal.vue";
import EnrolTypeModal from "@/components/EnrolTypeModal.vue";
import Toast from "@/components/Toast.vue";
import HelpIcon from "@/components/HelpIcon.vue";
import LFLoading from "@/components/LFLoading.vue";

import { useRouter, useRoute } from "vue-router";
import usersSvg from "@/assets/img/users.svg";
import plusSvg from "@/assets/img/plus.svg";
import {
  saveOffer,
  getOffer,
  getTypeOptions,
  searchAudiences,
  updateAudiences,
  // getAllAudiences não existe, usaremos searchAudiences('')
  addCourseToOffer,
  toggleCourseStatus,
  toggleClassStatus,
  removeCourseFromOffer,
  getCurrentCourses,
  getCategories,
  searchCurrentCoursesByName,
  searchCurrentCoursesByCategory,
  getClasses,
  deleteClass,
} from "@/services/offer";

export default {
  name: "NewOfferView",

  components: {
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomButton,
    Pagination,
    CollapsibleTable,
    PageHeader,
    BackButton,
    Autocomplete,
    TextEditor,
    CustomCheckbox,
    FilterRow,
    FilterGroup,
    FilterTag,
    FilterTags,
    AddCourseModal,
    ConfirmationModal,
    Toast,
    HelpIcon,
    DuplicateClassModal,
    EnrolTypeModal,
    LFLoading,
  },

  setup() {
    const router = useRouter();
    const route = useRoute();
    return { router, route };
  },

  directives: {
    tooltip: {
      mounted(el, binding) {
        el.setAttribute("title", binding.value);
      },
      updated(el, binding) {
        el.setAttribute("title", binding.value);
      },
    },
  },

  data() {
    return {
      // Ícones SVG
      icons: {
        users: usersSvg,
        plus: plusSvg,
      },
      showAddCourseModalVisible: false,
      showCourseStatusModal: false,
      selectedCourse: null,
      showWarning: true,
      isEditing: false,
      offerId: null,
      showDeleteCourseModal: false,
      courseToDelete: null,
      showDeleteClassModal: false,
      classToDelete: null,
      classParentCourse: null,
      showClassStatusModal: false,
      showDuplicateClassModal: false,
      showEnrolTypeModal: false,
      selectedClass: null,
      classToDuplicate: null,
      classToDuplicateParentCourse: null,
      selectedCourseForClass: null,
      loading: false,
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null, // Adicionar referência para o timeout

      // Dados da oferta
      offer: {
        id: null,
        name: "",
        offerType: "",
        description: "",
        status: 0,
      },

      // Controle de validação de campos
      formErrors: {
        name: { hasError: false, message: "Nome da oferta é obrigatório" },
        audiences: {
          hasError: false,
          message: "Selecione pelo menos um público-alvo",
        },
      },

      // Opções para selects
      offerTypeOptions: [],
      typeOptionsEnabled: false,

      audienceTypeOptions: [], // Esta será usada pelo Autocomplete
      allAudiences: [], // Armazenará todos os públicos carregados

      selectedAudiences: [],

      // Opções para os Autocomplete de categoria e curso
      categoryOptions: [],
      courseOptions: [],
      selectedCategory: null,
      selectedCourse: null,

      // Filtros de entrada (não aplicados)
      inputFilters: {
        course: "",
        category: "",
        onlyActive: false,
      },

      // Filtros aplicados
      appliedFilters: {
        course: "",
        category: "",
        onlyActive: false,
      },

      // Tabela de cursos
      courseTableHeaders: [
        { text: "NOME DO CURSO", value: "name", sortable: true },
        { text: "CATEGORIA", value: "category", sortable: true },
        { text: "NÚMERO DE TURMAS", value: "turmasCount", sortable: true },
        { text: "STATUS DO CURSO", value: "status", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],

      // Cursos selecionados (mockados)
      selectedCourses: [],

      // Paginação
      currentPage: 1,
      perPage: 5,
      totalItems: 0,

      // Ordenação
      sortBy: "id",
      sortDesc: false,

      // Paginação de cursos potenciais
      coursesPage: 1,
      coursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,
      loadingCourses: false,
      loadingMoreCourses: false,
      courseNoResultsText: "Nenhum curso encontrado",
    };
  },

  async mounted() {
    // Carregar dados iniciais
    await this.loadInitialData();

    // Carrega todos os públicos ao montar, se necessário
    if (this.offerId) {
      this.isEditing = true;
      await this.loadOfferData();

      // Carregar opções para os Autocomplete se estiver em modo de edição
      await this.loadCategoryOptions();
      await this.loadCourseOptions();
    } else {
      this.isEditing = false;
    }

    await this.loadTypeOptions();
    await this.loadAllAudiences(); // Carrega todos os públicos aqui

    // Mostrar aviso sempre, tanto para nova oferta quanto para edição
    this.showWarning = true;
  },

  computed: {
    canManageCourses() {
      return this.isEditing && this.offerId !== null;
    },

    hasActiveFilters() {
      // Agora considera apenas o filtro de curso, já que o filtro de categoria
      // é exibido no próprio componente Autocomplete
      return this.appliedFilters.course;
    },
  },

  methods: {
    /**
     * Retorna o nome amigável para um tipo de inscrição
     * @param {string} enrolType - Valor técnico do tipo de inscrição (ex: 'offer_manual')
     * @returns {string} Nome amigável (ex: 'Manual') ou o valor original se não encontrado
     */
    getEnrolTypeLabel(enrolType) {
      if (!enrolType) return "-";

      // Log para depuração

      // Verificar se o valor é uma string
      if (typeof enrolType !== "string") {
        return String(enrolType);
      }

      // Converter para minúsculas para comparação
      const enrolTypeLower = enrolType.toLowerCase();

      // Mapeamento de tipos de inscrição
      const enrolTypes = {
        offer_manual: "Manual",
        offer_self: "Autoinscrição",
        manual: "Manual",
        self: "Autoinscrição",
        guest: "Visitante",
        cohort: "Coorte",
        database: "Base de dados",
        flatfile: "Arquivo plano",
        ldap: "LDAP",
        lti: "LTI",
        meta: "Meta-curso",
        mnet: "MNet",
        paypal: "PayPal",
        shibboleth: "Shibboleth",
      };

      // Verificar correspondências exatas primeiro
      if (enrolTypes[enrolType]) {
        return enrolTypes[enrolType];
      }

      // Verificar correspondências ignorando maiúsculas/minúsculas
      for (const [key, value] of Object.entries(enrolTypes)) {
        if (key.toLowerCase() === enrolTypeLower) {
          return value;
        }
      }

      // Verificar correspondências específicas para o caso "Offer_manual"
      if (enrolTypeLower === "offer_manual") {
        return "Manual";
      }

      if (enrolTypeLower === "offer_self") {
        return "Autoinscrição";
      }

      // Verificar correspondências parciais (para casos como "Offer_manual" vs "offer_manual")
      for (const [key, value] of Object.entries(enrolTypes)) {
        if (enrolTypeLower.includes(key.toLowerCase())) {
          return value;
        }
      }

      // Se não encontrar correspondência, retornar o valor original
      return enrolType;
    },

    /**
     * Formata uma data timestamp para o formato DD/MM/YYYY
     * @param {number} timestamp - Timestamp em segundos
     * @returns {string} Data formatada ou '-' se não houver data
     */
    formatDate(timestamp) {
      if (!timestamp) return "-";

      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString("pt-BR");
    },

    async loadInitialData() {
      // Lógica para carregar dados iniciais gerais, se houver
      // Exemplo: this.loading = true; ... this.loading = false;
    },

    async loadOfferData() {
      if (!this.offerId) return;
      this.loading = true;
      try {
        const response = await getOffer(this.offerId);
        if (response && response.data) {
          this.offer = {
            id: response.data.id,
            name: response.data.name,
            offerType: response.data.typeid, // Ajuste conforme a API
            description: response.data.description || "",
            status: response.data.status || 0,
          };
          // Carregar públicos selecionados para esta oferta
          this.selectedAudiences = response.data.audiences
            ? response.data.audiences.map((a) => ({
                value: a.id,
                label: a.name,
              }))
            : [];
          await this.loadCourses(); // Carregar cursos associados
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar dados da oferta.");
      } finally {
        this.loading = false;
      }
    },

    async loadTypeOptions() {
      try {
        const response = await getTypeOptions();

        if (response && response.data) {
          const { enabled, types } = response.data;

          this.typeOptionsEnabled = !!enabled;

          if (enabled && Array.isArray(types)) {
            this.offerTypeOptions = types.map((type) => ({
              value: type,
              label: type.charAt(0).toUpperCase() + type.slice(1),
            }));
          } else {
            this.offerTypeOptions = [];
            this.typeOptionsEnabled = false;
          }
        } else {
          this.offerTypeOptions = [];
          this.typeOptionsEnabled = false;
        }
      } catch (error) {
        this.typeOptionsEnabled = false;
      }
    },

    async loadAllAudiences() {
      // Não recarrega se já tiver dados e não estiver forçando
      if (this.allAudiences.length > 0) {
        this.audienceTypeOptions = [...this.allAudiences]; // Garante que as opções estejam populadas
        return;
      }

      this.loading = true;
      try {
        // Assumindo que getAllAudiences retorna { data: [{ id: ..., name: ... }] }
        // Chama searchAudiences com query vazia para buscar todos
        const response = await searchAudiences("");
        // A resposta de searchAudiences tem a estrutura { items: [...] }
        if (response && response.items) {
          this.allAudiences = response.items.map((audience) => ({
            value: audience.id,
            label: audience.name,
          }));
          this.audienceTypeOptions = [...this.allAudiences]; // Popula as opções do autocomplete
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar públicos-alvo.");
      } finally {
        this.loading = false;
      }
    },

    async applyFilters() {
      // Aplicar os filtros de input aos filtros ativos
      this.appliedFilters = JSON.parse(JSON.stringify(this.inputFilters));

      try {
        this.loading = true;
        // Se tiver ambos os filtros
        if (
          this.appliedFilters.course &&
          this.appliedFilters.category &&
          this.appliedFilters.onlyActive
        ) {
          // Usar o método loadCourses que já tem suporte a paginação
          await this.loadCourses();
        }
        // Se tiver apenas filtro de curso
        else if (this.appliedFilters.course) {
          await this.loadCourses();
        }
        // Se tiver apenas filtro de categoria
        else if (this.appliedFilters.category) {
          await this.loadCourses();
        }
      } catch (error) {
        this.showErrorMessage(
          "Erro ao aplicar filtros. Por favor, tente novamente."
        );
      } finally {
        this.loading = false;
      }

      // Resetar a página para 1 quando aplicar filtros
      this.currentPage = 1;
    },

    clearFilters() {
      this.inputFilters = {
        course: "",
        category: "",
        onlyActive: false,
      };
      this.appliedFilters = {
        course: "",
        category: "",
        onlyActive: false,
      };

      // Limpar as seleções dos Autocomplete
      this.selectedCategory = null;
      this.selectedCourse = null;

      // Recarregar os cursos na tabela
      this.loadCourses();

      // Recarregar as opções de cursos sem filtro
      this.loadCourseOptions();
    },

    async removeFilter(filter) {
      try {
        this.loading = true;

        // Remover apenas o filtro específico mantendo os outros
        this.inputFilters[filter] = "";
        this.appliedFilters[filter] = "";

        // Limpar a seleção do Autocomplete correspondente
        if (filter === "category") {
          this.selectedCategory = null;
          // Recarregar as opções de cursos sem filtro de categoria
          await this.loadCourseOptions();
        } else if (filter === "course") {
          this.selectedCourse = null;
        }

        // Se ainda houver algum filtro ativo, reaplicar os filtros
        if (this.hasActiveFilters) {
          await this.applyFilters();
        } else {
          // Se não houver mais filtros ativos, recarregar a lista completa
          await this.loadCourses();
        }
      } catch (error) {
        this.showErrorMessage(
          "Erro ao remover filtro. Por favor, tente novamente."
        );
      } finally {
        this.loading = false;
      }
    },

    /**
     * Carrega as opções de categorias para o Autocomplete
     */
    async loadCategoryOptions() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        // Buscar todas as categorias disponíveis do backend
        const response = await getCategories("", this.offerId);

        if (response && response.data && Array.isArray(response.data)) {
          // Mapear as categorias para o formato esperado pelo Autocomplete
          // Garantindo que value seja sempre o ID da categoria
          this.categoryOptions = response.data.map((category) => ({
            value: category.id,
            label: category.name,
          }));
        } else {
          this.categoryOptions = [];
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar categorias.");
      } finally {
        this.loading = false;
      }
    },

    /**
     * Carrega as opções de cursos para o Autocomplete
     * @param {Object} categoryFilter - Categoria para filtrar os cursos (opcional)
     * @param {boolean} resetPagination - Se deve resetar a paginação
     */
    async loadCourseOptions(categoryFilter = null, resetPagination = true) {
      if (!this.offerId) return;

      try {
        this.loading = true;
        // Se temos uma categoria selecionada, filtrar os cursos por categoria
        if (categoryFilter) {
          await this.updateCourseOptionsByCategory(categoryFilter);
          return;
        }

        // Resetar a paginação se necessário
        if (resetPagination) {
          this.coursesPage = 1;
          this.coursesTotalPages = 1;
          this.hasMoreCourses = false;
          this.loadingCourses = true;
          this.courseOptions = []; // Limpa as opções existentes
        } else {
          this.loadingMoreCourses = true;
        }

        // Buscar cursos do backend com paginação
        const response = await getCurrentCourses(this.offerId, {
          onlyActive: false,
          page: this.coursesPage,
          perPage: this.coursesPerPage,
          sortBy: this.sortBy,
          sortDesc: this.sortDesc,
        });

        // Verificar se a resposta tem o novo formato (estrutura aninhada)
        if (response && response.data && response.data.courses) {
          // Novo formato: extrair informações de paginação
          const { page, total_pages, courses } = response.data;

          // Atualizar informações de paginação
          this.coursesPage = page || this.coursesPage;
          this.coursesTotalPages = total_pages || 1;
          this.hasMoreCourses = this.coursesPage < this.coursesTotalPages;

          // Mapear os cursos para o formato esperado pelo Autocomplete
          const newCourseOptions = courses.map((course) => ({
            value: course.id || course.courseid,
            label: course.fullname,
          }));

          // Adicionar os novos cursos aos existentes ou substituir completamente
          if (resetPagination) {
            this.courseOptions = newCourseOptions;
          } else {
            this.courseOptions = [...this.courseOptions, ...newCourseOptions];
          }

          // Atualizar o texto de "nenhum resultado"
          this.courseNoResultsText =
            this.courseOptions.length === 0
              ? "Nenhum curso disponível"
              : "Nenhum curso encontrado";
        }
        // Verificar o formato antigo (para compatibilidade)
        else if (response && response.data && Array.isArray(response.data)) {
          // Mapear os cursos para o formato esperado pelo Autocomplete
          const newCourseOptions = response.data.map((course) => ({
            value: course.id || course.courseid,
            label: course.fullname,
          }));

          // Adicionar os novos cursos aos existentes ou substituir completamente
          if (resetPagination) {
            this.courseOptions = newCourseOptions;
          } else {
            this.courseOptions = [...this.courseOptions, ...newCourseOptions];
          }

          // Assumir que não há mais páginas no formato antigo
          this.hasMoreCourses = false;

          // Atualizar o texto de "nenhum resultado"
          this.courseNoResultsText =
            this.courseOptions.length === 0
              ? "Nenhum curso disponível"
              : "Nenhum curso encontrado";
        } else {
          if (resetPagination) {
            this.courseOptions = [];
          }
          this.hasMoreCourses = false;
          this.courseNoResultsText = "Nenhum curso disponível";
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar cursos.");
        if (resetPagination) {
          this.courseOptions = [];
        }
        this.hasMoreCourses = false;
      } finally {
        if (resetPagination) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }
        this.loading = false;
      }
    },

    /**
     * Carrega mais cursos (próxima página) para o scroll infinito
     */
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        // Incrementar a página
        this.coursesPage += 1;

        // Se temos uma categoria selecionada
        if (this.selectedCategory) {
          // Carregar mais cursos da categoria
          await this.updateCourseOptionsByCategory(
            { value: this.selectedCategory, label: this.inputFilters.category },
            false
          );
        } else {
          // Carregar mais cursos sem filtro de categoria
          await this.loadCourseOptions(null, false);
        }
      }
    },

    /**
     * Manipula a busca por texto no autocomplete de cursos
     * @param {string} searchText - Texto digitado pelo usuário
     */
    async handleCourseSearch(searchText) {
      // Resetar a paginação
      this.coursesPage = 1;
      this.coursesTotalPages = 1;
      this.hasMoreCourses = false;
      this.loadingCourses = true;
      this.courseOptions = []; // Limpa as opções existentes

      try {
        // Se temos uma categoria selecionada
        if (this.selectedCategory) {
          // Buscar cursos da categoria com o termo de busca
          const categoryId = this.selectedCategory;
          const response = await searchCurrentCoursesByCategory(
            this.offerId,
            categoryId
          );

          if (response && response.data && Array.isArray(response.data)) {
            // Filtrar os cursos pelo termo de busca
            const filteredCourses = response.data.filter((course) =>
              course.fullname.toLowerCase().includes(searchText.toLowerCase())
            );

            // Mapear os cursos filtrados para o formato esperado pelo Autocomplete
            this.courseOptions = filteredCourses.map((course) => ({
              value: course.id || course.courseid,
              label: course.fullname,
            }));
          }
        } else {
          // Buscar cursos por nome com o termo de busca
          const response = await searchCurrentCoursesByName(
            this.offerId,
            searchText
          );

          if (response && response.data && Array.isArray(response.data)) {
            // Mapear os cursos para o formato esperado pelo Autocomplete
            this.courseOptions = response.data.map((course) => ({
              value: course.id || course.courseid,
              label: course.fullname,
            }));
          }
        }

        // Atualizar o texto de "nenhum resultado"
        this.courseNoResultsText =
          this.courseOptions.length === 0
            ? "Nenhum curso encontrado para a busca"
            : "Nenhum curso encontrado";
      } catch (error) {
        this.showErrorMessage("Erro ao buscar cursos.");
        this.courseOptions = [];
      } finally {
        this.loadingCourses = false;
      }
    },

    /**
     * Atualiza as opções do filtro de cursos com base na categoria selecionada
     * @param {Object} category - Categoria selecionada
     * @param {boolean} resetPagination - Se deve resetar a paginação
     */
    async updateCourseOptionsByCategory(category, resetPagination = true) {
      if (!this.offerId || !category) return;
      this.loading = true;
      try {
        // Configurar estado de carregamento
        if (resetPagination) {
          this.coursesPage = 1;
          this.coursesTotalPages = 1;
          this.hasMoreCourses = false;
          this.loadingCourses = true;
          this.courseOptions = []; // Limpa as opções existentes
        } else {
          this.loadingMoreCourses = true;
        }

        // Verificar se o valor da categoria é um número (ID) ou uma string (label)
        let categoryId = category.value;

        // Se o valor não for um número, precisamos buscar o ID da categoria pelo nome
        if (isNaN(parseInt(categoryId))) {
          // Buscar categorias para encontrar o ID correto
          const categoriesResponse = await getCategories(
            category.label,
            this.offerId
          );

          if (
            categoriesResponse &&
            categoriesResponse.data &&
            categoriesResponse.data.length > 0
          ) {
            // Encontrar a categoria que corresponde ao label
            const matchingCategory = categoriesResponse.data.find(
              (cat) => cat.name.toLowerCase() === category.label.toLowerCase()
            );

            if (matchingCategory) {
              categoryId = matchingCategory.id;
            } else {
              this.showErrorMessage(
                "Erro ao identificar a categoria selecionada."
              );
              if (resetPagination) {
                this.loadingCourses = false;
              } else {
                this.loadingMoreCourses = false;
              }
              return;
            }
          } else {
            this.showErrorMessage("Erro ao buscar categorias.");
            if (resetPagination) {
              this.loadingCourses = false;
            } else {
              this.loadingMoreCourses = false;
            }
            return;
          }
        }

        // Buscar cursos da categoria selecionada usando o ID correto
        const response = await searchCurrentCoursesByCategory(
          this.offerId,
          categoryId
        );

        if (response && response.data && Array.isArray(response.data)) {
          // Mapear os cursos para o formato esperado pelo Autocomplete
          const newCourseOptions = response.data.map((course) => ({
            value: course.id || course.courseid,
            label: course.fullname,
          }));

          // Adicionar os novos cursos aos existentes ou substituir completamente
          if (resetPagination) {
            this.courseOptions = newCourseOptions;

            // Limpar a seleção atual do filtro de cursos
            this.selectedCourse = null;
            this.inputFilters.course = "";
            this.appliedFilters.course = "";
          } else {
            this.courseOptions = [...this.courseOptions, ...newCourseOptions];
          }

          // Assumir que não há mais páginas para categorias
          this.hasMoreCourses = false;

          // Atualizar o texto de "nenhum resultado"
          this.courseNoResultsText =
            this.courseOptions.length === 0
              ? "Nenhum curso disponível nesta categoria"
              : "Nenhum curso encontrado";
        } else {
          if (resetPagination) {
            this.courseOptions = [];

            // Limpar a seleção atual do filtro de cursos
            this.selectedCourse = null;
            this.inputFilters.course = "";
            this.appliedFilters.course = "";
          }
          this.hasMoreCourses = false;
          this.courseNoResultsText = "Nenhum curso disponível nesta categoria";
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar cursos da categoria.");
        if (resetPagination) {
          this.courseOptions = [];
        }
        this.hasMoreCourses = false;
      } finally {
        if (resetPagination) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }
        this.loading = false;
      }
    },

    /**
     * Manipula a seleção de uma categoria no Autocomplete
     * @param {Object} category - Categoria selecionada
     */
    async handleCategorySelect(category) {
      if (!category) return;

      // Atualizar o filtro de categoria
      this.inputFilters.category = category.label;

      // Resetar a página para 1 quando mudar a categoria
      this.currentPage = 1;

      // Aplicar os filtros sem adicionar filterTag (a categoria já é exibida no próprio Autocomplete)
      await this.applyFilters();

      // Remover a categoria das filterTags, pois ela já é exibida no Autocomplete
      //this.appliedFilters.category = "";

      // Atualizar as opções do filtro de cursos para mostrar apenas os cursos da categoria selecionada
      // Resetar a paginação ao mudar de categoria
      await this.updateCourseOptionsByCategory(category, true);
    },

    /**
     * Manipula a seleção de um curso no Autocomplete
     * @param {Object} course - Curso selecionado
     */
    async handleCourseSelect(course) {
      if (!course) return;

      // Atualizar o filtro de curso
      this.inputFilters.course = course.label;

      // Resetar a página para 1 quando mudar o curso
      this.currentPage = 1;

      // Aplicar os filtros
      await this.applyFilters();
    },

    handleOnlyActiveChange() {
      // Atualizar o filtro de ativos
      this.appliedFilters.onlyActive = this.inputFilters.onlyActive;

      // Resetar a página para 1 quando mudar o filtro de ativos
      this.currentPage = 1;

      // Aplicar os filtros
      this.loadCourses();
    },

    goBack() {
      this.router.push({ name: "listar-ofertas" });
    },

    // Método para busca de cursos - chamado apenas quando o botão de pesquisa é clicado
    async searchCourses() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        // Primeiro buscar os IDs dos cursos que correspondem à busca
        const searchResponse = await searchCurrentCoursesByName(
          this.offerId,
          this.appliedFilters.course
        );

        if (
          searchResponse &&
          searchResponse.data &&
          searchResponse.data.length > 0
        ) {
          // Extrair os IDs dos cursos encontrados
          const courseIds = searchResponse.data.map((course) => course.id);

          // Buscar os dados completos dos cursos usando os IDs
          const coursesResponse = await getCurrentCourses(this.offerId, {
            courseIds: courseIds,
          });

          if (coursesResponse && coursesResponse.data.courses) {
            this.selectedCourses = coursesResponse.data.courses.map(
              (course) => ({
                id: course.id || course.courseid,
                offerCourseId: course.id,
                name: course.fullname,
                category: course.category_name || "-",
                turmasCount: Array.isArray(course.turmas)
                  ? course.turmas.length
                  : 0,
                status:
                  course.status === 1 || course.status === "1"
                    ? "Ativo"
                    : "Inativo",
                can_delete:
                  course.can_delete !== undefined ? course.can_delete : true,
                can_activate:
                  course.can_activate !== undefined
                    ? course.can_activate
                    : true,
                turmas: Array.isArray(course.turmas)
                  ? course.turmas.map((turma) => ({
                      id: turma.id,
                      nome: turma.name,
                      enrol_type: turma.enrol_type || "-",
                      vagas: turma.max_users ? turma.max_users : "Ilimitado",
                      inscritos: turma.enrolled_users || 0,
                      dataInicio: turma.start_date || "-",
                      dataFim: turma.end_date || "-",
                    }))
                  : [],
              })
            );
          } else {
            this.selectedCourses = [];
          }
        } else {
          this.selectedCourses = [];
        }
      } catch (error) {
        console.log(error);
        this.showErrorMessage(
          "Erro ao buscar cursos. Por favor, tente novamente."
        );
      } finally {
        this.loading = false;
      }
    },

    // Método para busca de categorias - chamado apenas quando o botão de pesquisa é clicado
    async searchCategories() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        // Buscar categorias pela API
        const response = await getCategories(
          this.appliedFilters.category,
          this.offerId
        );

        if (response && response.data && response.data.length > 0) {
          // Array para armazenar todos os cursos encontrados
          let allCourses = [];

          // Para cada categoria encontrada, buscar seus cursos
          for (const category of response.data) {
            // Garantir que estamos usando o ID da categoria
            const categoryId = category.id;

            if (!categoryId) {
              continue;
            }

            const coursesResponse = await searchCurrentCoursesByCategory(
              this.offerId,
              categoryId
            );

            if (coursesResponse && coursesResponse.data) {
              // Mapear os cursos da categoria atual e adicionar ao array
              const categoryCourses = coursesResponse.data.map((course) => ({
                id: course.id || course.courseid,
                offerCourseId: course.id,
                name: course.fullname,
                category: category.name || "-",
                turmasCount: Array.isArray(course.turmas)
                  ? course.turmas.length
                  : 0,
                status:
                  course.status === 1 || course.status === "1"
                    ? "Ativo"
                    : "Inativo",
                can_delete:
                  course.can_delete !== undefined ? course.can_delete : true,
                can_activate:
                  course.can_activate !== undefined
                    ? course.can_activate
                    : true,
                turmas: Array.isArray(course.turmas)
                  ? course.turmas.map((turma) => ({
                      id: turma.id,
                      nome: turma.name,
                      enrol_type: turma.enrol_type || "-",
                      vagas: turma.max_users ? turma.max_users : "Ilimitado",
                      inscritos: turma.enrolled_users || 0,
                      dataInicio: turma.start_date || "-",
                      dataFim: turma.end_date || "-",
                    }))
                  : [],
              }));

              // Adicionar os cursos da categoria ao array geral
              allCourses = [...allCourses, ...categoryCourses];
            }
          }

          // Atualizar a lista de cursos com todos os cursos encontrados
          this.selectedCourses = allCourses;
        } else {
          this.selectedCourses = [];
        }
      } catch (error) {
        this.showErrorMessage(
          "Erro ao buscar categorias. Por favor, tente novamente."
        );
      } finally {
        this.loading = false;
      }
    },

    showAddCourseModal() {
      this.showAddCourseModalVisible = true;
    },

    async handleAddCourseConfirm(selectedCourses) {
      try {
        this.loading = true;

        for (const course of selectedCourses) {
          await addCourseToOffer(this.offerId, course);
        }

        // Recarregar os cursos da oferta para atualizar a lista
        await this.loadCourses();

        // Atualizar as opções dos Autocomplete com os novos cursos
        await this.loadCategoryOptions();
        await this.loadCourseOptions();

        // Exibir mensagem de sucesso com a quantidade de cursos adicionados
        // Mensagem genérica de sucesso
        this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.");
      } catch (error) {
        this.showErrorMessage(
          error.message || "Ocorreu um erro ao adicionar os cursos."
        );
      } finally {
        this.loading = false;
      }
    },

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
      this.loadCourses();
    },

    /**
     * Manipula a mudança de página na paginação
     * @param {number} page - Número da página selecionada
     */
    handlePageChange(page) {
      this.currentPage = page;

      // Caso contrário, carregar os cursos normalmente
      this.loadCourses();
    },

    addTurma(course) {
      // Armazena o curso selecionado e mostra o modal de seleção de tipo de inscrição

      this.selectedCourseForClass = course;
      this.showEnrolTypeModal = true;
    },

    handleEnrolTypeConfirm(data) {
      // Fecha o modal
      this.showEnrolTypeModal = false;

      // Redireciona para a página de criação de turma com o tipo de inscrição selecionado
      this.router.push({
        name: "NewClass",
        params: {
          offercourseid: data.offercourseid,
          offerid: data.offerid || this.offerId || "0",
        },
        query: {
          enrol_type: data.enrolType,
        },
      });
    },

    editTurma(turma) {
      // Redirecionar para a página de edição de turma
      // Encontrar o curso pai da turma
      const parentCourse = this.selectedCourses.find(
        (course) =>
          course.turmas && course.turmas.some((t) => t.id === turma.id)
      );

      if (parentCourse) {
        // Redirecionar para a página de edição de turma usando a nova rota
        this.router.push({
          name: "EditClass",
          params: {
            offercourseid: parentCourse.offerCourseId,
            classid: turma.id,
            offerid: this.offerId || "0",
          },
        });
      } else {
        this.showErrorMessage(
          "Não foi possível editar a turma. Curso pai não encontrado."
        );
      }
    },

    toggleClassStatus(turma) {
      // Armazenar a turma para uso no método de confirmação
      this.selectedClass = {
        ...turma,
        status: turma.status || "Ativo", // Definindo um status padrão se não existir
      };

      // Mostrar o modal de confirmação
      this.showClassStatusModal = true;
    },

    async confirmToggleClassStatus() {
      if (!this.selectedClass) return;

      try {
        this.loading = true;
        const turmaNome = this.selectedClass.nome;
        const newStatus = this.selectedClass.status === "Ativo" ? false : true;

        // Chamar a API para alterar o status da turma
        await toggleClassStatus(this.selectedClass.id, newStatus);

        // Atualizar o status da turma na lista local
        const courseIndex = this.selectedCourses.findIndex((c) =>
          c.turmas.some((t) => t.id === this.selectedClass.id)
        );

        if (courseIndex !== -1) {
          const course = this.selectedCourses[courseIndex];
          const turmaIndex = course.turmas.findIndex(
            (t) => t.id === this.selectedClass.id
          );

          if (turmaIndex !== -1) {
            course.turmas[turmaIndex].status = newStatus ? "Ativo" : "Inativo";
          }
        }

        // Recarregar os dados da oferta para sincronizar com o servidor
        await this.loadCourses();

        // Exibir mensagem de sucesso
        this.showSuccessMessage(
          newStatus
            ? `Turma "${turmaNome}" ativada com sucesso.`
            : `Turma "${turmaNome}" inativada com sucesso.`
        );

        this.selectedClass = null;
        this.showClassStatusModal = false;
      } catch (error) {
        this.showErrorMessage(
          error.message || "Erro ao alterar status da turma."
        );
      } finally {
        this.loading = false;
      }
    },

    removeTurma(course, turmaIndex) {
      const turma = course.turmas[turmaIndex];

      // Verificar se a turma pode ser excluída
      if (!turma.can_delete) {
        return;
      }

      // Armazenar a turma e o curso para uso no método de confirmação
      this.classToDelete = turma;
      this.classParentCourse = course;

      // Mostrar o modal de confirmação
      this.showDeleteClassModal = true;
    },

    viewRegisteredUsers(turma) {
      // Navegar para a página de usuários matriculados
      this.router.push({
        name: "usuarios-matriculados",
        params: {
          offerclassid: turma.id,
        },
      });
    },

    // Método simplificado para abrir o modal de duplicação de turma
    duplicateTurma(turma, parentCourse) {
      // Armazenar a turma e o curso para uso no componente
      this.classToDuplicate = turma;
      this.classToDuplicateParentCourse = parentCourse;

      // Mostrar o modal de duplicação
      this.showDuplicateClassModal = true;
    },

    // Método para tratar o sucesso da duplicação de turma
    async handleDuplicateSuccess(data) {
      // Recarregar os dados da oferta para mostrar as novas turmas
      await this.loadCourses();

      // Verificar se é uma duplicação múltipla ou única
      if (data.duplicatedCount) {
        // Duplicação múltipla
        this.showSuccessMessage(
          `Turma "${data.turmaNome}" duplicada com sucesso para ${data.duplicatedCount} curso(s).`
        );
      } else {
        // Duplicação única (formato antigo)
        this.showSuccessMessage(
          `Turma "${data.turmaNome}" duplicada com sucesso para o curso "${data.targetCourseName}".`
        );
      }
    },

    async confirmDeleteClass() {
      if (!this.classToDelete || !this.classParentCourse) return;

      try {
        this.loading = true;
        const turmaNome = this.classToDelete.nome;

        // Chamar a API para excluir a turma
        await deleteClass(this.classToDelete.id);

        // Encontrar o índice da turma no array de turmas do curso
        const turmaIndex = this.classParentCourse.turmas.findIndex(
          (t) => t.id === this.classToDelete.id
        );

        if (turmaIndex !== -1) {
          // Remover a turma da lista local
          this.classParentCourse.turmas.splice(turmaIndex, 1);
          this.classParentCourse.turmasCount =
            this.classParentCourse.turmas.length;
        }

        // Mostrar mensagem de sucesso
        this.showSuccessMessage(`Turma ${turmaNome} excluída com sucesso.`);

        // Limpar os dados e fechar o modal
        this.classToDelete = null;
        this.classParentCourse = null;
        this.showDeleteClassModal = false;
      } catch (error) {
        this.showErrorMessage(error.message || "Erro ao excluir turma.");
      } finally {
        this.loading = false;
      }
    },

    /**
     * Atualiza a contagem de turmas de um curso
     */
    updateTurmasCount(courseId) {
      const courseIndex = this.selectedCourses.findIndex(
        (course) => course.id === courseId
      );
      if (courseIndex !== -1) {
        const course = this.selectedCourses[courseIndex];
        course.turmasCount = Array.isArray(course.turmas)
          ? course.turmas.length
          : 0;
        this.selectedCourses[courseIndex] = { ...course };
      }
    },

    async loadCourses() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        const options = {
          onlyActive: this.appliedFilters.onlyActive,
          page: this.currentPage,
          perPage: this.perPage,
          sortBy: this.sortBy,
          sortDesc: this.sortDesc,
        };

        // Quando loadCourses é chamado sem interação direta do usuário com os campos de busca,
        // os valores de courseSearch e categorySearch serão ignorados
        if (this.appliedFilters.course) {
          options.courseSearch = this.appliedFilters.course;
        }
        if (this.appliedFilters.category) {
          options.categorySearch = this.appliedFilters.category;
        }

        // Usar a busca padrão sem filtros adicionais
        const response = await getCurrentCourses(this.offerId, options);

        // Verificar se a resposta tem o formato esperado
        if (response && response.data) {
          let courses = [];
          let page = this.currentPage;
          let total_pages = 1;
          let total_items = 0;

          // Verificar se a resposta tem o novo formato (estrutura aninhada)
          if (response.data.courses) {
            ({ page, total_pages, total_items, courses } = response.data);
          }

          // Atualizar informações de paginação
          this.currentPage = page || this.currentPage;

          // Array para armazenar os cursos processados
          const processedCourses = [];

          // Para cada curso, buscar suas turmas
          for (const course of courses) {
            try {
              // Buscar turmas do curso
              const turmasResponse = await getClasses(course.id);

              // Processar as turmas se a resposta for válida
              let turmas = [];

              // Verificar se a resposta tem o formato esperado e extrair as turmas
              if (turmasResponse) {
                if (
                  typeof turmasResponse === "object" &&
                  turmasResponse.error === false &&
                  Array.isArray(turmasResponse.data) &&
                  turmasResponse.data.length > 0
                ) {
                  turmas = turmasResponse.data.map((turma) => {
                    // Determinar o valor correto para enrol_type
                    let enrolTypeValue = turma.enrol_type || turma.enrol || "-";

                    return {
                      id: turma.id,
                      nome: turma.name,
                      enrol_type: enrolTypeValue,
                      vagas: turma.max_users ? turma.max_users : "Ilimitado",
                      inscritos: turma.enrolled_users || 0,
                      dataInicio: this.formatDate(turma.startdate),
                      dataFim: this.formatDate(turma.enddate),
                      status:
                        turma.status === 0 || turma.status === "0"
                          ? "Inativo"
                          : "Ativo",
                      can_activate:
                        turma.can_activate !== undefined
                          ? turma.can_activate
                          : true,
                      can_delete:
                        turma.can_delete !== undefined
                          ? turma.can_delete
                          : true,
                    };
                  });
                }
              }

              // Adicionar o curso processado ao array
              processedCourses.push({
                id: course.courseid || course.id,
                offerCourseId: course.id,
                name: course.fullname,
                category: course.category_name || course.category || "-",
                turmasCount: turmas.length,
                status:
                  course.status === 1 || course.status === "1"
                    ? "Ativo"
                    : "Inativo",
                can_delete:
                  course.can_delete !== undefined ? course.can_delete : true,
                can_activate:
                  course.can_activate !== undefined
                    ? course.can_activate
                    : true,
                turmas: turmas,
              });
            } catch (error) {
              // Adicionar o curso sem turmas em caso de erro
              processedCourses.push({
                id: course.courseid || course.id,
                offerCourseId: course.id,
                name: course.fullname,
                category: course.category_name || course.category || "-",
                turmasCount: 0,
                status:
                  course.status === 1 || course.status === "1"
                    ? "Ativo"
                    : "Inativo",
                can_delete:
                  course.can_delete !== undefined ? course.can_delete : true,
                can_activate:
                  course.can_activate !== undefined
                    ? course.can_activate
                    : true,
                turmas: [],
              });
            }
          }

          // Atualizar a lista de cursos com os dados processados
          this.selectedCourses = processedCourses;

          // Calcular o total de itens com base no total_pages e perPage
          // Se total_items estiver disponível, usá-lo diretamente
          if (total_items !== undefined && total_items !== null) {
            this.totalItems = total_items;
          } else if (total_pages > 0) {
            // Se não tiver total_items mas tiver total_pages, calcular aproximadamente
            this.totalItems = total_pages * this.perPage;
          } else {
            // Fallback para o número de cursos carregados
            this.totalItems = processedCourses.length;
          }

          // Atualizar as opções dos Autocomplete com os dados carregados
          await this.loadCategoryOptions();
          await this.loadCourseOptions();
        } else {
          this.selectedCourses = [];
          this.totalItems = 0;

          // Limpar as opções dos Autocomplete
          this.categoryOptions = [];
          this.courseOptions = [];
        }
      } catch (error) {
        this.showErrorMessage(
          "Erro ao carregar cursos da oferta. Por favor, tente novamente."
        );
        this.selectedCourses = [];
        this.totalItems = 0;
      } finally {
        this.loading = false;
      }
    },

    async loadOffer(id) {
      try {
        this.loading = true;

        // Limpar os filtros ao carregar a oferta
        this.inputFilters = {
          course: "",
          category: "",
        };
        this.appliedFilters = {
          course: "",
          category: "",
        };

        // Limpar as seleções dos Autocomplete
        this.selectedCategory = null;
        this.selectedCourse = null;

        const [offerResponse, audiencesData] = await Promise.all([
          getOffer(id),
          searchAudiences(""),
        ]);

        // Extrair dados da oferta do array de resposta
        const offerData = Array.isArray(offerResponse)
          ? offerResponse[0]
          : offerResponse;

        if (!offerData.error && offerData.data) {
          const offer = offerData.data;

          this.offer = {
            name: offer.name,
            offerType: offer.type, // Mantém compatibilidade com o binding do componente
            description: offer.description,
            id: offer.id,
            status: offer.status,
          };

          if (audiencesData && Array.isArray(audiencesData.items)) {
            const selectedAudienceIds = offer.audiences || [];
            this.selectedAudiences = audiencesData.items
              .filter((item) => selectedAudienceIds.includes(item.id))
              .map((item) => ({
                value: item.id,
                label: item.name.toUpperCase(),
              }));
          }

          this.isEditing = true;

          // Carregar os cursos da oferta
          await this.loadCourses();
        } else {
          throw new Error(
            offerData.message || "Erro ao carregar dados da oferta"
          );
        }
      } catch (error) {
        this.showErrorMessage(error.message || "Erro ao carregar oferta.");
      } finally {
        this.loading = false;
      }
    },
    handleSelectAllAudiences() {
      // Seleciona todos os públicos-alvo reais (excluindo a opção "Todos" que é virtual)
      // Garante que não haja duplicatas se já houver itens selecionados
      const allRealAudienceValues = new Set(
        this.allAudiences.map((a) => a.value)
      );
      const currentSelectedValues = new Set(
        this.selectedAudiences.map((a) => a.value)
      );

      // Verifica se todos já estão selecionados
      let allSelected = true;
      for (const value of allRealAudienceValues) {
        if (!currentSelectedValues.has(value)) {
          allSelected = false;
          break;
        }
      }

      if (
        allSelected &&
        this.selectedAudiences.length === this.allAudiences.length
      ) {
        // Se todos já estão selecionados, desmarca todos
        this.selectedAudiences = [];
      } else {
        // Senão, seleciona todos
        this.selectedAudiences = [...this.allAudiences];
      }
    },

    /**
     * Valida todos os campos obrigatórios
     * @returns {boolean} true se todos os campos são válidos, false caso contrário
     */
    validate() {
      // Resetar todos os erros
      Object.keys(this.formErrors).forEach((key) => {
        this.formErrors[key].hasError = false;
      });

      // Flag para controlar se há erros
      let hasErrors = false;

      // Validar nome da oferta
      if (!this.offer.name) {
        this.formErrors.name.hasError = true;
        hasErrors = true;
      }

      // Validar públicos-alvo
      if (this.selectedAudiences.length === 0) {
        this.formErrors.audiences.hasError = true;
        hasErrors = true;
      }

      // Se houver erros, mostrar mensagem de erro no toast
      if (hasErrors) {
        this.showErrorMessage("Há campos obrigatórios a serem preenchidos.");
      }

      return !hasErrors;
    },

    /**
     * Valida um campo específico
     * @param {string} field Nome do campo a ser validado
     */
    validateField(field) {
      // Validar campo específico
      switch (field) {
        case "name":
          this.formErrors.name.hasError = !this.offer.name;
          break;
        case "audiences":
          this.formErrors.audiences.hasError =
            this.selectedAudiences.length === 0;
          break;
      }

      // Retorna se o campo específico não tem erro
      return !this.formErrors[field].hasError;
    },

    // Lógica para salvar a oferta
    async saveOffer() {
      this.loading = true;

      // Validação completa
      if (!this.validate()) {
        this.loading = false;
        return;
      }

      try {
        const offerData = {
          name: this.offer.name,
          description: this.offer.description,
          type: this.offer.offerType, // Ajustado para 'type' conforme esperado pela API
          status: this.offer.status, // Ou o status padrão desejado
          audiences: this.selectedAudiences.map((a) => a.value), // Envia apenas os IDs
        };

        let response;
        if (this.isEditing && this.offerId) {
          // Atualizar oferta existente
          // 1. Primeiro atualiza os dados gerais da oferta
          offerData.id = this.offerId; // Adiciona o ID da oferta para atualização

          response = await saveOffer(offerData);

          if (response && !response.error) {
            // 2. Depois atualiza os públicos-alvo
            const audienceResponse = await this.updateOfferAudiences();

            if (audienceResponse) {
              this.showSuccessMessage("Oferta atualizada com sucesso!");
            } else {
              throw new Error(
                "Oferta atualizada, mas houve falha ao atualizar públicos-alvo."
              );
            }
          } else {
            const errorMessage =
              response?.message ||
              response?.error ||
              "Falha ao atualizar oferta.";
            throw new Error(errorMessage);
          }
        } else {
          // Criar nova oferta
          response = await saveOffer(offerData); // Usa a função importada saveOffer
          if (response && response.data && response.data.id) {
            const newOfferId = response.data.id;
            this.offerId = newOfferId; // Armazena o ID da nova oferta
            this.offer.id = newOfferId;
            this.isEditing = true; // Muda para o modo de edição
            this.showSuccessMessage("Oferta salva com sucesso!");
            // Atualiza a URL sem recarregar a página (melhor UX)
            // Usar o caminho direto em vez do nome da rota para evitar problemas
            const newPath = `/edit-offer/${this.offerId}`;

            this.router.replace(newPath);
          } else {
            // Tenta extrair mensagem de erro da API, se houver
            const errorMessage =
              response?.message ||
              response?.[0]?.message ||
              "Falha ao salvar a oferta.";
            throw new Error(errorMessage);
          }
        }
      } catch (error) {
        this.showErrorMessage(
          error.message ||
            "Erro ao salvar oferta. Verifique os dados e tente novamente."
        );
      } finally {
        this.loading = false;
      }
    },

    async updateOfferAudiences() {
      // Função para atualizar os públicos-alvo da oferta
      if (!this.offerId) return false;

      try {
        // Obter os IDs dos públicos selecionados
        const audienceIds = this.selectedAudiences.map((a) => a.value);

        // Chamar a API para atualizar os públicos-alvo
        const response = await updateAudiences(this.offerId, audienceIds);

        if (response && !response.error) {
          return true; // Indica sucesso
        } else {
          return false; // Indica falha
        }
      } catch (error) {
        this.showErrorMessage("Erro ao atualizar públicos-alvo.");
        return false; // Indica falha
      } finally {
        // Não mudar loading aqui se saveOffer controla
      }
    },

    toggleCourseStatus(course) {
      if (!course.can_activate) {
        return;
      }
      this.selectedCourse = course;
      this.showCourseStatusModal = true;
    },

    getStatusButtonTitle(item) {
      if (item.status === "Ativo") {
        return item.can_activate
          ? "Inativar"
          : "Não é possível inativar este curso";
      } else {
        return item.can_activate
          ? "Ativar"
          : "Não é possível ativar este curso";
      }
    },

    async confirmToggleCourseStatus() {
      if (this.selectedCourse) {
        try {
          this.loading = true;
          const newStatus =
            this.selectedCourse.status === "Ativo" ? false : true;
          const courseNome = this.selectedCourse.name;

          // Usar o offerCourseId para identificação correta do curso na oferta
          const courseId =
            this.selectedCourse.offerCourseId || this.selectedCourse.id;

          await toggleCourseStatus(this.offerId, courseId, newStatus);

          // Atualiza o status do curso na lista local para feedback imediato
          const courseIndex = this.selectedCourses.findIndex(
            (c) => c.id === this.selectedCourse.id
          );
          if (courseIndex !== -1) {
            this.selectedCourses[courseIndex].status = newStatus
              ? "Ativo"
              : "Inativo";
          }

          // Fechar o modal e limpar a seleção ANTES de recarregar os dados
          this.showCourseStatusModal = false;
          this.selectedCourse = null;

          // Recarregar os dados da oferta para sincronizar com o servidor
          await this.loadCourses();

          // Atualizar as opções dos Autocomplete após alterar o status do curso
          await this.loadCategoryOptions();
          await this.loadCourseOptions();

          // Exibir mensagem de sucesso
          this.showSuccessMessage(
            newStatus
              ? `Curso "${courseNome}" ativado com sucesso.`
              : `Curso "${courseNome}" inativado com sucesso.`
          );
        } catch (error) {
          this.showErrorMessage(
            error.message || "Erro ao alterar status do curso."
          );
        } finally {
          this.loading = false;
        }
      }
    },

    deleteCourse(course) {
      if (!course.can_delete) {
        return;
      }
      this.courseToDelete = course;
      this.showDeleteCourseModal = true;
    },

    async confirmDeleteCourse() {
      if (this.courseToDelete) {
        try {
          this.loading = true;
          const courseNome = this.courseToDelete.name;

          // Usar o offerCourseId para identificação correta do curso na oferta
          const courseId =
            this.courseToDelete.offerCourseId || this.courseToDelete.id;

          await removeCourseFromOffer(this.offerId, courseId);

          // Remove o curso da lista local para feedback imediato
          this.selectedCourses = this.selectedCourses.filter(
            (c) => c.id !== this.courseToDelete.id
          );

          // Recarregar os dados da oferta para sincronizar com o servidor
          await this.loadCourses();

          // Atualizar as opções dos Autocomplete após remover o curso
          await this.loadCategoryOptions();
          await this.loadCourseOptions();

          // Exibir mensagem de sucesso
          this.showSuccessMessage(
            `Curso "${courseNome}" excluído com sucesso.`
          );

          this.courseToDelete = null;
          this.showDeleteCourseModal = false;
        } catch (error) {
          this.showErrorMessage(error.message || "Erro ao remover curso.");
        } finally {
          this.loading = false;
        }
      }
    },

    /**
     * Exibe uma mensagem de sucesso usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      // Isso ajuda a reiniciar a animação corretamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000); // Usar a duração definida no componente Toast se necessário
      });
    },

    /**
     * Exibe uma mensagem de erro usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },
  },

  async created() {
    try {
      // Carregar opções de tipos de oferta
      const response = await getTypeOptions();

      if (response && response.data) {
        const { enabled, types, default: defaultType } = response.data;

        this.typeOptionsEnabled = enabled;

        if (enabled && Array.isArray(types)) {
          this.offerTypeOptions = types.map((type) => ({
            value: type,
            label: type,
          }));

          // Se houver um tipo padrão e não estiver editando, usar ele
          if (defaultType && !this.isEditing) {
            this.offer.offerType = defaultType;
          }
        }
      }
    } catch (error) {
      this.showErrorMessage(
        error.message || "Erro ao carregar opções de tipos."
      );
    }

    // Verificar se há um ID na rota
    const id = this.route.params.id;
    if (id) {
      this.offerId = parseInt(id);
      this.loadOffer(this.offerId);
    }
  },

  watch: {
    "inputFilters.course"(newValue, oldValue) {
      // Se o usuário apagou completamente o campo, recarregar a lista completa
      if (newValue.length === 0 && oldValue.length > 0) {
        this.loadCourses();
      }
    },
    "inputFilters.category"(newValue, oldValue) {
      // Se o usuário apagou completamente o campo, recarregar a lista completa
      if (newValue.length === 0 && oldValue.length > 0) {
        this.loadCourses();
      }
    },
    selectedCategory(newValue) {
      // Se o usuário limpar a seleção, limpar o filtro correspondente
      if (!newValue) {
        this.inputFilters.category = "";
        if (this.appliedFilters.category) {
          this.appliedFilters.category = "";
          this.loadCourses();
        }

        // Recarregar as opções de cursos sem filtro de categoria
        this.loadCourseOptions();
      }
    },
    selectedCourse(newValue) {
      // Se o usuário limpar a seleção, limpar o filtro correspondente
      if (!newValue) {
        this.inputFilters.course = "";
        if (this.appliedFilters.course) {
          this.appliedFilters.course = "";
          this.loadCourses();
        }
      }
    },
    currentPage() {
      this.loadCourses();
    },
    perPage() {
      // Resetar para a primeira página quando mudar o número de itens por página
      this.currentPage = 1;
      // Recarregar os dados
      this.loadCourses();
    },
  },
};
</script>

<style lang="scss" scoped>
.new-offer {
  margin-bottom: 2rem;
}

.alert {
  padding: 1rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  gap: 0.5rem;

  &.alert-warning {
    background-color: #332701;
    border: 1px solid #997404;
    color: #ffda6a;
  }

  i {
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
  }
}

.section-container {
  margin-bottom: 2rem;
  background-color: #212529;
  border-radius: 4px;
}

.section-title {
  color: var(--primary);
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.filter-label {
  font-size: 14px;
  color: #fff;
  margin-bottom: 5px;
}

.course-actions {
  display: flex;
  align-items: center;
  gap: 1rem;

  .no-results {
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.empty-state {
  padding: 2rem;
  text-align: center;

  .no-results {
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.text-editor-container {
  margin-bottom: 1rem;
}

.turmas-container {
  background-color: #343a40;
}

.turmas-header,
.turmas-row {
  display: flex;
  border-bottom: 1px solid #495057;

  &:last-child {
    border-bottom: none;
  }
}

.turmas-header {
  background-color: #212529;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.turmas-content {
  background-color: #343a40;
}

.turmas-row {
  height: 50px;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.05);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.empty-turmas {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.turma-col {
  flex: 1;
  padding: 0.75rem 0.5rem;
  font-size: 14px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;

  /* Estilos específicos que sobrescrevem o global.scss */
  .btn-action {
    font-size: 1.25rem;
    color: #fff;

    &.btn-add {
      img {
        width: 20px;
        height: 20px;
      }
    }

    &:disabled {
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}

.btn-activate {
  color: #6c757d !important;

  &:hover {
    background-color: rgba(108, 117, 125, 0.1) !important;
  }

  i {
    opacity: 0.7;
  }
}

.btn-deactivate {
  color: #fff !important;
}

.pagination-container {
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  // width: 100%;

  .pagination-info {
    font-size: 14px;
    color: #6c757d;
  }

  .pagination-controls {
    display: flex;
    gap: 0.5rem;

    .page-item {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      height: 32px;
      padding: 0 0.5rem;
      border-radius: 4px;
      background-color: transparent;
      color: #fff;
      font-size: 14px;
      cursor: pointer;

      &.active {
        background-color: var(--primary);
        color: #fff;
      }

      &:hover:not(.active) {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.section-disabled {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.offer-actions {
  margin: 2rem 0;
}

.courses-filter-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 1rem;
}
.filters-left-group {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  flex-wrap: wrap;

  .mt-3 {
    margin-top: 1rem;
    width: 100%;
  }

  /* Regras CSS :deep() para limitar o input foram removidas, agora controlado por prop */
  .autocomplete-audiences {
    :deep(.filter-tags) {
      margin-bottom: 16px; /* Mantém a margem inferior para as tags, se necessário */
    }
  }

  /* Garante que o componente Autocomplete mantenha o mesmo tamanho quando uma categoria é selecionada */
  :deep(.autocomplete-container) {
    width: 250px;
    min-width: 250px;
  }

  /* Garante que o grupo de checkbox não seja afetado pelas alterações no Autocomplete */
  :deep(.checkbox-group) {
    width: auto !important;
    min-width: auto !important;

    .checkbox-container {
      width: auto !important;
      min-width: auto !important;
    }
  }

  /* Estilo específico para o grupo de checkbox "Não exibir inativos" */
  .checkbox-filter-group {
    margin-left: 15px !important;

    :deep(.checkbox-container) {
      width: auto !important;
      min-width: auto !important;
      margin-bottom: 0 !important;
    }
  }

  :deep(.filter-tags) {
    margin-top: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  :deep(.filter-tag) {
    background-color: #343a40;
    border: 1px solid #495057;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;

    .remove-tag {
      cursor: pointer;
      opacity: 0.7;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }
}

.filters-right-group {
  display: flex;
  align-items: flex-end;
}

.label-with-help {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  gap: 2px;
}

.form-label {
  font-size: 14px;
  color: #fff !important;
  margin-bottom: 0;
  margin-right: 2px;
}

.fa-exclamation-circle {
  margin-right: 0;
}

.required-fields-message {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.form-info {
  background-color: #212529;
  color: #fff;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.limited-width-input {
  max-width: 280px;
}

.limited-width-editor {
  max-width: 700px;
}

.label-container {
  margin-bottom: 0.5rem;
}

.input-container {
  width: 100%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
  min-width: 280px;
}

.message-container {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.lock-message {
  background: #2c3136;
  padding: 1rem 2rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #3a3f45;

  span {
    color: #f8f9fa;
    font-size: 0.95rem;
    font-weight: 500;
    letter-spacing: 0.2px;
  }
}

.lock-icon {
  font-size: 1.5rem;
  color: #ffc107; /* Amarelo mais vibrante para o ícone do cadeado */
  text-shadow: 0 0 5px rgba(255, 193, 7, 0.5); /* Efeito de brilho */
}

.no-title-section {
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}

/* Os estilos do modal de duplicação foram movidos para o componente DuplicateClassModal.vue */
</style>
