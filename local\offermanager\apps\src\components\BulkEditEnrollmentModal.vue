<template>
  <div v-if="show" class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">Edição de Matrículas em Lote</h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body">
        <div class="enrollment-form">
          <div>
            <div class="table-container">
              <CustomTable :headers="tableHeaders" :items="filteredUsers" />
            </div>

            <Pagination
              v-if="users.length > 0"
              v-show="users.length > perPage"
              v-model:current-page="currentPage"
              v-model:per-page="perPage"
              :total="users.length"
            />

            <span class="d-block w-100 border-bottom mt-4"></span>
          </div>

          <!-- Estado (ativo/inativo) -->
          <div class="form-row">
            <div class="form-label">Alterar o status</div>
            <div class="form-field">
              <div class="select-wrapper">
                <CustomSelect
                  v-model="formData.status"
                  :options="statusOptions"
                  :width="235"
                  class="smaller-select"
                />
              </div>
            </div>
          </div>

          <!-- Data de início da matrícula -->
          <div class="form-row">
            <div class="form-label">Alterar data de início</div>
            <div class="form-field date-time-field">
              <div class="date-field">
                <input
                  type="date"
                  v-model="formData.startDateStr"
                  class="form-control"
                  @change="handleStartDateChange"
                  :disabled="!formData.enableStartDate"
                />
              </div>
              <div class="time-field">
                <input
                  type="time"
                  v-model="formData.startTimeStr"
                  class="form-control"
                  @change="handleStartTimeChange"
                  :disabled="!formData.enableStartDate"
                />
              </div>
              <div class="enable-checkbox">
                <input
                  type="checkbox"
                  id="enable-start-date"
                  v-model="formData.enableStartDate"
                  class="custom-checkbox"
                />
                <label for="enable-start-date">Habilitar</label>
              </div>
            </div>
          </div>

          <!-- Matrícula termina -->
          <div class="form-row">
            <div class="form-label">Alterar data de fim</div>
            <div class="form-field date-time-field">
              <div class="date-field">
                <input
                  type="date"
                  v-model="formData.endDateStr"
                  class="form-control"
                  @change="handleEndDateChange"
                  :disabled="!formData.enableEndDate"
                />
              </div>
              <div class="time-field">
                <input
                  type="time"
                  v-model="formData.endTimeStr"
                  class="form-control"
                  @change="handleEndTimeChange"
                  :disabled="!formData.enableEndDate"
                />
              </div>
              <div class="enable-checkbox">
                <input
                  type="checkbox"
                  id="enable-end-date"
                  v-model="formData.enableEndDate"
                  class="custom-checkbox"
                />
                <label for="enable-end-date">Habilitar</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Rodapé do modal -->
      <div class="modal-footer">
        <div class="footer-spacer"></div>
        <div class="footer-buttons">
          <button
            class="btn btn-primary"
            @click="saveChanges"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? "Salvando..." : "Salvar mudanças" }}
          </button>
          <button class="btn btn-secondary" @click="$emit('close')">
            Cancelar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "./Pagination.vue";
import CustomTable from "./CustomTable.vue";
import CustomSelect from "./CustomSelect.vue";
import { editEnrolmentBulk } from "@/services/enrolment";

export default {
  name: "BulkEditEnrollmentModal",
  components: {
    Pagination,
    CustomTable,
    CustomSelect,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    users: {
      type: Array,
      default: () => [],
    },
    offerclassid: {
      type: [Number, String],
      required: true,
    },
  },
  emits: ["close", "success", "error"],
  data() {
    return {
      isSubmitting: false,
      formData: {
        status: "1",
        startDateStr: "",
        startTimeStr: "00:00",
        enableStartDate: false,
        endDateStr: "",
        endTimeStr: "23:59",
        enableEndDate: false,
      },
      statusOptions: [
        { value: 1, label: "Ativo" },
        { value: 0, label: "Suspenso" },
      ],

      currentPage: 1,
      perPage: 5,
      sortBy: "fullName",
      sortDesc: false,

      tableHeaders: [
        { text: "NOME/SOBRENOME", value: "fullName", sortable: false },
        { text: "ESTADO ", value: "stateName", sortable: false },
        {
          text: "INÍCIO DA MATRÍCULA",
          value: "startDate",
          sortable: false,
        },
        { text: "FIM DA MATRÍCULA", value: "endDate", sortable: false },
      ],
    };
  },
  computed: {
    filteredUsers() {
      // Aplicar ordenação
      const sortedCourses = [...this.users].sort((a, b) => {
        const modifier = this.sortDesc ? -1 : 1;
        if (a[this.sortBy] < b[this.sortBy]) return -1 * modifier;
        if (a[this.sortBy] > b[this.sortBy]) return 1 * modifier;
        return 0;
      });

      // Aplicar paginação
      const startIndex = (this.currentPage - 1) * this.perPage;
      const endIndex = startIndex + this.perPage;
      return sortedCourses.slice(startIndex, endIndex);
    },
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.initializeForm();
      }
    },
  },
  methods: {
    initializeForm() {
      // Inicializar com valores padrão
      const now = new Date();

      this.formData = {
        status: "1", // Ativo por padrão
        startDateStr: this.formatDateForInput(now),
        startTimeStr: "00:00",
        enableStartDate: false,
        endDateStr: this.formatDateForInput(now),
        endTimeStr: "23:59",
        enableEndDate: false,
      };
    },

    formatDateForInput(date) {
      return date.toISOString().split("T")[0];
    },

    formatTimeForInput(date) {
      return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },

    handleStartDateChange() {
      // Nada a fazer aqui, apenas para manter o evento
    },

    handleStartTimeChange() {
      // Nada a fazer aqui, apenas para manter o evento
    },

    handleEndDateChange() {
      // Nada a fazer aqui, apenas para manter o evento
    },

    handleEndTimeChange() {
      // Nada a fazer aqui, apenas para manter o evento
    },

    async saveChanges() {
      if (!this.users || this.users.length === 0) {
        console.error("Nenhum usuário selecionado");
        this.$emit("error", "Nenhum usuário selecionado para edição em lote.");
        return;
      }

      try {
        this.isSubmitting = true;

        // Preparar os dados para envio
        const status = parseInt(this.formData.status);

        // Calcular timestamp de início
        let timestart = 0;
        if (this.formData.enableStartDate && this.formData.startDateStr) {
          // Criar a data usando UTC para evitar problemas de fuso horário
          const [year, month, day] = this.formData.startDateStr
            .split("-")
            .map(Number);
          const [hours, minutes] = this.formData.startTimeStr
            .split(":")
            .map(Number);

          // Criar a data no fuso horário local
          const startDate = new Date(
            year,
            month - 1,
            day,
            hours,
            minutes,
            0,
            0
          );

          // Converter para timestamp sem ajuste de fuso horário
          timestart = Math.floor(startDate.getTime() / 1000);

          // Adicionar o offset do fuso horário para compensar a conversão do PHP
          const timezoneOffset = startDate.getTimezoneOffset() * 60;
          timestart += timezoneOffset;
        }

        // Calcular timestamp de fim
        let timeend = 0;
        if (this.formData.enableEndDate && this.formData.endDateStr) {
          // Criar a data usando UTC para evitar problemas de fuso horário
          const [year, month, day] = this.formData.endDateStr
            .split("-")
            .map(Number);
          const [hours, minutes] = this.formData.endTimeStr
            .split(":")
            .map(Number);

          // Criar a data no fuso horário local
          const endDate = new Date(year, month - 1, day, hours, minutes, 0, 0);

          // Converter para timestamp sem ajuste de fuso horário
          timeend = Math.floor(endDate.getTime() / 1000);

          // Adicionar o offset do fuso horário para compensar a conversão do PHP
          const timezoneOffset = endDate.getTimezoneOffset() * 60;
          timeend += timezoneOffset;
        }

        // Extrair os IDs das matrículas dos usuários
        const offeruserenrolids = this.users
          .filter((user) => user.offeruserenrolid)
          .map((user) => user.offeruserenrolid);

        if (offeruserenrolids.length === 0) {
          console.error("Nenhum ID de matrícula encontrado");
          this.$emit(
            "error",
            "Não foi possível encontrar os IDs das matrículas dos usuários selecionados."
          );
          return;
        }

        // Chamar a API para editar as matrículas em lote
        const response = await editEnrolmentBulk({
          offeruserenrolids: offeruserenrolids,
          status: status,
          timestart: timestart,
          timeend: timeend,
        });

        // Verificar se a operação foi bem-sucedida
        if (Array.isArray(response) && response.length > 0) {
          const successCount = response.filter(
            (result) => result.operation_status
          ).length;
          const failCount = response.length - successCount;

          let message = "";
          if (successCount === response.length) {
            message = `${successCount} matrícula(s) editada(s) com sucesso.`;
          } else if (successCount > 0) {
            message = `${successCount} de ${response.length} matrícula(s) editada(s) com sucesso. ${failCount} matrícula(s) não puderam ser editadas.`;
          } else {
            message = "Nenhuma matrícula pôde ser editada.";
            this.$emit("error", message);
            return;
          }

          this.$emit("success", {
            message: message,
            count: successCount,
            total: response.length,
          });
          this.$emit("close");
        } else {
          console.error("Resposta inválida da API:", response);
          this.$emit(
            "error",
            "Não foi possível editar as matrículas. Por favor, tente novamente."
          );
        }
      } catch (error) {
        console.error("Erro ao salvar alterações:", error);
        this.$emit(
          "error",
          "Ocorreu um erro ao editar as matrículas. Por favor, tente novamente."
        );
      } finally {
        this.isSubmitting = false;
      }
    },
  },
};
</script>

<style lang="scss">
/* Estilo global para corrigir o problema da setinha */
.custom-select[data-v-944e90b] {
  width: auto !important;
  max-width: 120px !important;
}
</style>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 700px;
  border: 1px solid #373b3e;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #373b3e;
  background-color: #212529;
  position: relative;

  .modal-title {
    margin: 0;
    font-size: 18.75px;
    font-weight: bold;
    color: #f8f9fa;
  }

  .modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);

    i {
      font-size: 0.9rem;
    }

    &:hover {
      color: #ffffff;
      opacity: 0.8;
    }
  }
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;

  .table-container {
    margin-bottom: 0 !important;
  }
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-top: 1px solid #373b3e;
  background-color: #212529;

  .footer-spacer {
    flex: 1;
  }

  .footer-buttons {
    display: flex;
    gap: 10px;
  }

  .btn {
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .btn-primary {
    font-size: 15px;

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  .btn-secondary {
    color: #000;
  }
}

.enrollment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #f8f9fa;
  font-size: 15px;
  min-width: 220px;
  flex-shrink: 0;
}

.form-value {
  color: #f8f9fa;
  font-size: 15px;
  flex: 1;
}

.form-field {
  flex: 1;

  /* Sobrescrever o estilo global dos selects */
  :deep(.custom-select) {
    width: auto !important;
  }
}

.date-time-field {
  display: flex;
  gap: 10px;
  align-items: center;
}

.date-field {
  position: relative;
  flex: 2;

  input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    background-color: #212529;
    border: 1px solid #373b3e;
    border-radius: 4px;
    color: #fff;
    font-size: 0.875rem;
  }

  /* Estilo para o input de data */
  input[type="date"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px; /* Espaço para o ícone de calendário nativo */
    position: relative;
    cursor: pointer;
  }

  /* Estilo para o ícone de calendário nativo */
  input[type="date"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    color: var(--primary);
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
  }
}

.time-field {
  position: relative;
  flex: 1;

  input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    background-color: #212529;
    border: 1px solid #373b3e;
    border-radius: 4px;
    color: #fff;
    font-size: 0.875rem;
  }

  /* Estilo para o input de hora */
  input[type="time"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px; /* Espaço para o ícone de relógio nativo */
    position: relative;
    cursor: pointer;
  }

  /* Estilo para o ícone de relógio nativo */
  input[type="time"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    color: var(--primary);
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
  }
}

.enable-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  width: 100px;

  input[type="checkbox"] {
    margin: 0;
  }

  label {
    color: #fff;
    font-size: 0.875rem;
    margin: 0;
  }
}

.custom-checkbox {
  width: 20px;
  height: 20px;
  background-color: #212529;
  border: 1px solid #373b3e;
  border-radius: 3px;
  appearance: none;
  cursor: pointer;
  position: relative;

  &:checked {
    background-color: var(--primary);
    border-color: var(--primary);

    &::after {
      content: "";
      position: absolute;
      left: 7px;
      top: 3px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }
}

/* Wrapper para os selects */
.select-wrapper {
  width: 120px !important;
  position: relative !important;
  display: inline-block !important;

  :deep(.custom-select) {
    width: 120px !important;
    max-width: 120px !important;
  }

  :deep(.select-container) {
    width: 120px !important;
    max-width: 120px !important;
    height: 28px !important;
    overflow: hidden !important;
    position: relative !important;
  }

  :deep(.select-value) {
    max-width: 90px !important; /* Deixa espaço para a setinha */
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    display: inline-block !important;
  }

  :deep(.select-arrow) {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 16px !important;
    height: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 2 !important;
  }

  /* Sobrescrever o estilo global que pode estar causando o problema */
  :deep([data-v-944e90b]) {
    width: 120px !important;
  }
}

/* Estilo para selects menores */
.smaller-select {
  :deep(.select-container) {
    height: 28px !important;
    font-size: 12px !important;
    background-color: #212529 !important;
    border: 1px solid #373b3e !important;
    border-radius: 4px !important;
    color: #fff !important;
  }

  :deep(.select-value) {
    padding: 0 6px !important;
    font-size: 12px !important;
  }

  :deep(.select-dropdown) {
    font-size: 12px !important;
    max-width: 120px !important;
    width: 120px !important;
    background-color: #212529 !important;
    border: 1px solid #373b3e !important;
  }

  :deep(.select-option) {
    padding: 5px 6px !important;
    color: #fff !important;

    &:hover,
    &.selected {
      background-color: var(--primary) !important;
    }
  }
}

/* Estilo para campos desabilitados */
.disabled-field {
  opacity: 0.6;
  pointer-events: none;
}

/* Estilo para desabilitar apenas os inputs, não o checkbox */
.disabled-inputs-only {
  .date-field,
  .time-field {
    opacity: 0.6;
    pointer-events: none;
  }

  .enable-checkbox {
    opacity: 1;
    pointer-events: auto;
  }
}
</style>
