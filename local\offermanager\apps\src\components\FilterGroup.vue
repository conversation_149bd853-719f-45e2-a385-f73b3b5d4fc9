<template>
  <div class="filter-group" :class="{ 'checkbox-group': isCheckbox }">
    <div v-if="label" class="filter-label">{{ label }}</div>
    <div class="filter-input">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterGroup',

  props: {
    label: {
      type: String,
      default: ''
    },
    isCheckbox: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;

  &.checkbox-group {
    flex-direction: row;
    align-items: flex-end;
    height: 100%;
    margin-left: 10px;
    min-width: auto; /* Garante que o grupo de checkbox não seja afetado pela largura mínima do Autocomplete */
    width: auto; /* Permite que o grupo de checkbox tenha apenas a largura necessária */
  }

  @media (max-width: 768px) {
    width: 100%;
    margin-right: 0;
  }
}

.filter-label {
  font-size: 14px;
  color: #fff !important;
  margin-bottom: 8px;
  font-weight: 500;
}

.filter-input {
  position: relative;
  display: flex;
  align-items: center;
  width: auto;

  input,
  select {
    padding: 10px 12px;
    background-color: #212529 !important;
    border: 1px solid #495057 !important;
    border-radius: 4px;
    color: #fff !important;
    height: 42px;
    font-size: 15px;
    font-weight: 400;
  }

  input {
    max-width: 336px;
  }

  select {
    max-width: 144px;
  }

  input::placeholder,
  select::placeholder {
    color: #6c757d !important;
  }
}
</style>