<template>
  <div class="page-header">
    <h2>{{ title }}</h2>
    <div class="header-actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON>Header',
  props: {
    title: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  color: #fff !important;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
  
  h1 {
    font-size: 32px;
    font-weight: 500;
    font-family: 'Helvetica Neue', sans-serif;
    margin: 0;
    color: #fff !important;
    text-align: left;
  }
  
  .header-actions {
    margin-left: auto;
    display: flex;
    justify-content: flex-end;
    
    @media (max-width: 768px) {
      width: 100%;
      margin-left: 0;
      margin-top: 1rem;
    }
  }
}
</style> 