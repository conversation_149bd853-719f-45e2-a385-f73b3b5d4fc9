<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use local_offermanager\constants;
use local_offermanager\event\offer_created;
use local_offermanager\event\offer_updated;
use local_offermanager\event\offer_deleted;
use local_offermanager\event\offer_activated;
use local_offermanager\event\offer_inactivated;
use context_system;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_model
 *
 * @package    local_offermanager
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_model extends persistent
{

    /** Nome da tabela no banco de dados */
    const TABLE = 'local_offermanager';

    /**
     * Define a estrutura do objeto.
     *
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'name' => [
                'type' => PARAM_TEXT,
                'description' => 'Nome da oferta',
                'null' => NULL_ALLOWED,
                'default' => constants::OFFER_NAME_DEFAULT
            ],
            'description' => [
                'type' => PARAM_TEXT,
                'description' => 'Descrição da oferta',
                'null' => NULL_ALLOWED,
                'default' => constants::OFFER_DESCRIPTION_DEFAULT
            ],
            'status' => [
                'type' => PARAM_INT,
                'description' => 'Status da oferta',
                'choice' => [constants::OFFER_STATUS_INACTIVE, constants::OFFER_STATUS_ACTIVE],
                'null' => NULL_NOT_ALLOWED,
                'default' => constants::OFFER_STATUS_DEFAULT
            ],
            'type' => [
                'type' => PARAM_TEXT,
                'description' => 'Tipo da oferta',
                'null' => NULL_ALLOWED,
                'default' => constants::OFFER_TYPE_DEFAULT
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id do creador',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;

                    return $USER->id;
                }
            ]
        ];
    }

    /**
     * Retorna as ofertas associadas as parâmeros de entrada.
     *
     * @param string $serach_string 
     * @param string|null $type
     * @param bool $only_active
     * @param int $page
     * @param int $per_page
     * @param string $sort_by
     * @param string $sort_direction
     * 
     * @return offer_model[]
     */

    public static function fetch(
        string $search_string = '',
        string|null $type = null,
        bool $only_active = false,
        int $page = 0,
        int $per_page = 25,
        string $sort_by = 'name',
        string $sort_direction = 'ASC'
    ): array {

        $params = [];
        $select = '1=1';

        if ($search_string) {
            $params['search_string'] = '%' . $search_string . '%';
            $select .= " AND name LIKE :search_string";
        }

        if ($type) {
            $params['type'] = $type;
            $select .= " AND type = :type";
        }

        if ($only_active) {
            $params['status'] = constants::OFFER_STATUS_ACTIVE;
            $select .= " AND status = :status";
        }

        $lower_limit = $page * $per_page;

        return self::get_records_select(
            $select,
            $params,
            "{$sort_by} {$sort_direction}",
            '*',
            $lower_limit,
            $per_page
        );
    }

    /**
     * Retorna as ofertas associadas as parâmeros de entrada.
     *
     * @param string $serach_string 
     * @param string|null $type
     * @param bool $only_active
     * 
     * @return int
     */

    public static function count_offers(
        string $search_string = '',
        string|null $type = null,
        bool $only_active = false

    ): int {

        $params = [];
        $select = '1=1';

        if ($search_string) {
            $params['search_string'] = '%' . $search_string . '%';
            $select .= " AND name LIKE :search_string";
        }

        if ($type) {
            $params['type'] = $type;
            $select .= " AND type = :type";
        }

        if ($only_active) {
            $params['status'] = constants::OFFER_STATUS_ACTIVE;
            $select .= " AND status = :status";
        }

        return self::count_records_select(
            $select,
            $params
        );
    }

    public static function fetch_types(string $search_string = '', bool $only_active = false)
    {
        global $DB;

        $conditions = [];
        $params = [];

        if ($search_string) {
            $conditions[] = $DB->sql_like('name', ':search_string', false, false);
            $params['search_string'] = '%' . $DB->sql_like_escape($search_string) . '%';
        }

        if ($only_active) {
            $conditions[] = 'status = :status';
            $params['status'] = constants::OFFER_STATUS_ACTIVE;
        }

        $select = implode(' AND ', $conditions);

        if (empty($select)) {
            $select = '1=1';
        }

        return $DB->get_fieldset_select(
            self::TABLE,
            'DISTINCT type',
            $select,
            $params
        );
    }

    /**
     * Retorna os públicos-alvo associados a esta oferta.
     * 
     * @param int $audience_id
     * @return offer_audience_model|bool
     */
    public function get_audience(int $audienceid): offer_audience_model|bool
    {
        return offer_audience_model::get_record(
            [
                'offerid' => $this->get('id'),
                'audienceid' => $audienceid
            ]
        );
    }

    /**
     * Verifica se existe o públicos-alvo associado a esta oferta.
     * 
     * @param int $audience_id
     * @return bool
     */
    public function has_audience(int $audienceid): bool
    {
        return !!offer_audience_model::record_exists_select(
            'offerid = :offerid AND audienceid = :audienceid',
            [
                'offerid' => $this->get('id'),
                'audienceid' => $audienceid
            ]
        );
    }

    /**
     * Retorna os públicos-alvo associados a esta oferta.
     *
     * @return offer_audience_model[]
     */
    public function get_audiences()
    {
        return offer_audience_model::get_records(
            [
                'offerid' => $this->get('id')
            ]
        );
    }

    /**
     * Verifica se existe o públicos-alvo associado a esta oferta.
     * 
     * @return bool
     */
    public function has_audiences(): bool
    {
        return offer_audience_model::record_exists_select(
            'offerid = :offerid',
            [
                'offerid' => $this->get('id')
            ]
        );
    }

    /**
     * Retorna os ids dos públicos-alvo associados a esta oferta.
     *
     * @return int[]
     */
    public function get_audience_ids()
    {
        $audiences = $this->get_audiences();

        return array_map(
            function ($audience) {

                return $audience->get('audienceid');
            },
            $audiences
        );
    }

    /**
     * Atualiza as relações de público-alvo (audience) de forma diferencial.
     *
     * @param int[] $audience_ids Lista de IDs dos públicos-alvo selecionados.
     * @return bool
     */
    public function update_audiences(array $audienceids): bool
    {

        $current_audience_ids = $this->get_audience_ids();

        $audience_ids = array_map('intval', $audienceids);

        $audience_ids_to_add = array_diff($audience_ids, $current_audience_ids);

        $audience_ids_to_remove = array_diff($current_audience_ids, $audience_ids);

        foreach ($audience_ids_to_add as $audience_id) {
            $this->add_audience($audience_id);
        }

        foreach ($audience_ids_to_remove as $audience_id) {
            $this->remove_audience($audience_id);
        }

        return !!($audience_ids_to_add || $audience_ids_to_remove);
    }

    /**
     * Adiciona um público-alvo a esta oferta.
     *
     * @param int $audienceid ID do público-alvo.
     * @return offer_audience_model
     * @throws moodle_exception
     */
    public function add_audience(int $audienceid): offer_audience_model
    {
        $offer_audience = $this->get_audience($audienceid);

        if ($offer_audience) {
            throw new moodle_exception('error:duplicate_offer_audience', 'local_offermanager');
        }

        $offer_audience = new offer_audience_model(0, (object) [
            'offerid' => $this->get('id'),
            'audienceid' => $audienceid,
        ]);

        $offer_audience->save();

        return $offer_audience;
    }

    /**
     * Remove um público-alvo desta oferta.
     *
     * @param int $audienceid ID do público-alvo.
     * @return bool
     * @throws moodle_exception
     */
    public function remove_audience(int $audienceid): bool
    {
        $offer_audience = $this->get_audience($audienceid);

        if (!$offer_audience) {
            throw new moodle_exception('error:offer_audience_not_found', 'local_offermanager');
        }

        return $offer_audience->delete();
    }

    public function fetch_courses(
        int $categoryid = 0,
        string $search_string = '',
        array $exclude_courseids = []
    ) {
        global $DB;

        $select = "WHERE loc.offerid = ?";
        $join = "JOIN {course} c ON (loc.courseid = c.id)";

        $params[] = $this->get('id');

        if ($categoryid) {
            $join .= " LEFT JOIN {course_categories} cc ON (c.category = cc.id)";
            $select .= " AND cc.id = ?";
            $params[] = $categoryid;
        }

        if ($search_string) {
            $fullname_like = $DB->sql_like('c.fullname', '?', false, false);
            $shortname_like = $DB->sql_like('c.shortname', '?', false, false);
            $select .= " AND ({$fullname_like} OR {$shortname_like})";
            $params[] = "%{$search_string}%";
            $params[] = "%{$search_string}%";
        }

        if (!empty($exclude_courseids) && is_array($exclude_courseids)) {

            [$insql, $inparams] = $DB->get_in_or_equal(
                $exclude_courseids,
                SQL_PARAMS_QM,
                'param',
                false
            );

            $select .= " AND loc.courseid {$insql}";

            $params = array_merge($params, $inparams);
        }

        return array_values($DB->get_records_sql(
            "SELECT
                loc.courseid as id,
                c.fullname as fullname
            FROM {" . offer_course_model::TABLE . "} loc
                $join
            $select
            ",
            $params
        ));
    }

    /**
     * Retorna os cursos associados a esta oferta.
     *
     * @return offer_course_model[]
     */
    public function get_courses(
        bool $only_active = false,
        array $courseids = [],
        int $page = 0,
        int $per_page = 0,
        string $sort_by = 'id',
        string $sort_direction = 'ASC',
        string $course_search = '',
        string $category_search = ''
    ) {
        global $DB;

        $table = offer_course_model::TABLE;
        $select = "mof.offerid = ?";

        $params[] = $this->get('id');

        if (!empty($courseids) && is_array($courseids)) {

            [$insql, $inparams] = $DB->get_in_or_equal($courseids);

            $select .= " AND courseid {$insql}";

            $params = array_merge($params, $inparams);
        }

        if ($only_active) {
            $activestatus = constants::OFFER_STATUS_ACTIVE;
            $select .= " AND mof.status = {$activestatus}";
        }

        if ($course_search) {
            $fullname_like = $DB->sql_like('mc.fullname', '?', false, false);
            $select .= " AND {$fullname_like}";
            $params[] = "%{$course_search}%";
        }

        if ($category_search) {
            $category_like = $DB->sql_like('mcc.name', '?', false, false);
            $select .= " AND {$category_like}";
            $params[] = "%{$category_search}%";
        }

        if ($sort_by === 'status') {
            $sort_by = "FIELD(mof.status, '1', '0')";
        }

        $page = $per_page * $page;

        $sql = "SELECT 
                    mof.*, mc.fullname, mcc.name as category, COUNT(mofc.id) AS class_counter
                FROM {{$table}} mof
                    JOIN {course} mc ON (mc.id = mof.courseid)
                    JOIN {course_categories} mcc ON (mc.category = mcc.id)
                    LEFT JOIN {local_offermanager_class} mofc ON (mofc.offercourseid = mof.id)
                WHERE $select
                GROUP BY mof.id
                ORDER BY $sort_by $sort_direction";

        $records = $DB->get_records_sql($sql, $params, $page, $per_page);

        $courses = [];

        foreach ($records as $record) {
            $newrecord = new offer_course_model(0, $record);
            array_push($courses, $newrecord);
        }

        return array_values($courses);
    }

    /**
     * Retorna as categorias dos cursos associados a esta oferta.
     * 
     * @param string $search_string Texto para busca por nome de curso.
     * 
     * @return array[]
     */
    public function get_categories(string $search_string = '')
    {
        global $DB;

        $params = [
            'offerid' => $this->get('id')
        ];

        $where = "WHERE EXISTS (
                SELECT id
                FROM {" . offer_course_model::TABLE . "} loc
                WHERE c.id = loc.courseid
                AND loc.offerid = :offerid
            )
        ";

        if ($search_string) {
            $where = ' AND ' . $DB->sql_like('cc.name', ':search_string', false, false);
            $params['search_string'] = "%$search_string%";
        }

        return array_values($DB->get_records_sql(
            "SELECT cc.id, cc.name
            FROM {course_categories} cc
                JOIN {course} c ON (c.category = cc.id)
            $where
            ",
            $params
        ));
    }

    /**
     * Adiciona um curso a esta oferta.
     *
     * @param int $courseid ID do público-alvo.
     * @return offer_course_model
     */
    public function add_course($courseid)
    {
        $offer_course = new offer_course_model(0, (object) [
            'offerid' => $this->get('id'),
            'courseid' => $courseid,
        ]);

        $offer_course->save();

        return $offer_course;
    }

    /**
     * Remove um curso desta oferta.
     *
     * @param int $courseid ID do público-alvo.
     * @return bool
     */
    public function remove_course($courseid)
    {
        $offer_course = offer_audience_model::get_record([
            'offerid' => $this->get('id'),
            'courseid' => $courseid,
        ]);

        if (!$offer_course) {
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager');
        }

        return $offer_course->delete();
    }

    public function has_courses()
    {
        return !!$this->get_courses();
    }

    public function has_enrol_instances(): bool
    {
        $offer_courses = $this->get_courses();

        if (!$offer_courses) {
            return false;
        }

        foreach ($offer_courses as $offer_course) {
            if ($offer_course->has_enrol_instances()) {
                return true;
            }
        }

        return false;
    }

    public function has_user_enrolments(): bool
    {
        $offer_courses = $this->get_courses();

        if (!$offer_courses) {
            return false;
        }

        foreach ($offer_courses as $offer_course) {
            if ($offer_course->has_user_enrolments()) {
                return true;
            }
        }

        return false;
    }

    public function can_delete()
    {
        return (!$this->has_enrol_instances() || !$this->has_user_enrolments())
            && has_capability('local/offermanager:manage', context_system::instance());
    }

    protected function validate_name($value)
    {
        if (!$this->get('id')) {
            $value || throw new moodle_exception('error:offer_name_required', 'local_offermanager');
        }

        return is_string($value);
    }

    /**
     * Valida o status da oferta antes de salvar.
     *
     * @param int $value
     * @return bool
     * @throws moodle_exception
     */
    protected function validate_status(int $value): bool
    {
        if ($value === constants::OFFER_STATUS_ACTIVE && !$this->can_activate()) {
            throw new moodle_exception('error:cannot_activate_offer', 'local_offermanager');
        }

        return true;
    }

    /**
     * Verifica se a oferta pode ser ativada.
     *
     * @return bool
     */
    public function can_activate(): bool
    {

        if (empty($this->get_audiences())) {
            return false;
        }

        $courses = $this->get_courses();

        if (!$courses) {
            return false;
        }

        foreach ($courses as $course) {
            if (!$course->has_enrol_instances()) {
                return false;
            }
        }

        return has_capability('local/offermanager:manage', context_system::instance());
    }


    /**
     * Verifica se a instância está ativa.
     *
     * @return bool
     */
    public function is_active()
    {
        return $this->get('status') == constants::OFFER_STATUS_ACTIVE;
    }


    /**
     * Ativa a oferta, se as condições forem atendidas.
     *
     * @return bool
     * @throws moodle_exception
     */
    public function activate(): bool
    {
        if ($this->get('status') === constants::OFFER_STATUS_ACTIVE) {
            throw new moodle_exception('error:offer_already_active', 'local_offermanager');
        }

        if (!$this->can_activate()) {
            throw new moodle_exception('error:cannot_activate_offer', 'local_offermanager');
        }
        $this->enable_enrol_instances();

        $this->set('status', constants::OFFER_STATUS_ACTIVE);
        $this->save();

        return $this->get('status') == constants::OFFER_STATUS_ACTIVE;
    }

    /**
     * Inativa a oferta.
     *
     * @return bool
     * @throws moodle_exception
     */
    public function inactivate(): bool
    {
        if ($this->get('status') === constants::OFFER_STATUS_INACTIVE) {
            throw new moodle_exception('error:offer_already_inactive', 'local_offermanager');
        }

        $this->set('status', constants::OFFER_STATUS_INACTIVE);

        $this->save();

        return $this->get('status') == constants::OFFER_STATUS_INACTIVE;
    }

    protected function after_create()
    {
        $event = offer_created::instance($this);
        $event->trigger();
    }

    protected function before_update()
    {
        global $DB;

        $table_status = $DB->get_field(
            self::TABLE,
            'status',
            [
                'id' => $this->get('id')
            ]
        );

        $instance_status = $this->get('status');

        if ($table_status != $instance_status) {

            $event =  $instance_status == constants::OFFER_STATUS_ACTIVE
                ? offer_activated::instance($this)
                : offer_inactivated::instance($this);

            $event->trigger();
        }
    }

    protected function after_update($result)
    {
        if ($result) {
            $event = offer_updated::instance($this);
            $event->trigger();
        }
    }

    protected function before_delete()
    {
        if (!$this->can_delete()) {
            throw new moodle_exception('error:cannot_delete_offer', 'local_offermanager');
        }

        if ($this->has_audiences()) {
            foreach ($this->get_audiences() as $audience) {
                $audience->delete();
            }
        }

        if ($this->has_courses()) {
            foreach ($this->get_courses() as $course) {
                $course->delete();
            }
        }

        $event = offer_deleted::instance($this);
        $event->trigger();
    }

    /**
     * Fetches potential courses not yet associated with this offer.
     *
     * @param int $categoryid Category ID to filter by (0 for all).
     * @param string $search_string Search string for course shortname or fullname.
     * @param array $exclude_courseids Array of course IDs to exclude.
     * @param int $page Page number (0-based).
     * @param int $perpage Number of courses per page.
     * @return array An array of course objects.
     */
    public function fetch_potential_courses(
        int $categoryid = 0,
        string $search_string = '',
        array $exclude_courseids = [],
        int $page = 0,
        int $perpage = 20
    ) {
        global $DB;

        [$and, $params] = $this->build_potential_courses_query_conditions($categoryid, $search_string, $exclude_courseids);

        $limitfrom = $page * $perpage;
        $limitnum = $perpage;

        return array_values($DB->get_records_sql(
            "SELECT c.id, c.fullname
            FROM {course} c
            WHERE c.id > 1
                AND c.visible = 1
            AND NOT EXISTS (
                SELECT id
                FROM {" . offer_course_model::TABLE . "} loc
                WHERE c.id = loc.courseid
                    AND loc.offerid = {$this->get('id')}
            )
            {$and}
            ORDER BY c.fullname",
            $params,
            $limitfrom,
            $limitnum
        ));
    }

    /**
     * Counts potential courses not yet associated with this offer.
     *
     * @param int $categoryid Category ID to filter by (0 for all).
     * @param string $search_string Search string for course shortname or fullname.
     * @param array $exclude_courseids Array of course IDs to exclude.
     * @return int The total number of potential courses.
     */
    public function count_potential_courses(
        int $categoryid = 0,
        string $search_string = '',
        array $exclude_courseids = []
    ): int {
        global $DB;

        [$and, $params] = $this->build_potential_courses_query_conditions($categoryid, $search_string, $exclude_courseids);

        $sql = "SELECT COUNT(c.id)
                FROM {course} c
                WHERE c.id > 1
                AND NOT EXISTS (
                    SELECT id
                    FROM {" . offer_course_model::TABLE . "} loc
                    WHERE c.id = loc.courseid
                        AND loc.offerid = {$this->get('id')}
                )
                {$and}";

        return (int) $DB->count_records_sql($sql, $params);
    }

    /**
     * Builds the SQL query conditions and parameters for fetching/counting potential courses.
     *
     * @param int $categoryid Category ID to filter by (0 for all).
     * @param string $search_string Search string for course shortname or fullname.
     * @param array $exclude_courseids Array of course IDs to exclude.
     * @return array An array containing the SQL 'AND' clause string and the parameters array.
     */
    private function build_potential_courses_query_conditions(
        int $categoryid = 0,
        string $search_string = '',
        array $exclude_courseids = []
    ): array {
        global $DB;

        $and = " AND c.visible = 1 AND c.format <> 'gallery'";
        $params = [];

        if ($categoryid) {
            $and .= " AND c.category = ? ";
            $params[] = $categoryid;
        }

        if ($search_string) {
            $and .= " AND (LOWER(c.shortname) LIKE LOWER(?) OR LOWER(c.fullname) LIKE LOWER(?)) COLLATE utf8mb4_unicode_ci";
            $params[] = "%$search_string%";
            $params[] = "%$search_string%";
        }

        if (!empty($exclude_courseids) && is_array($exclude_courseids)) {
            [$insql, $inparams] = $DB->get_in_or_equal(
                $exclude_courseids,
                SQL_PARAMS_QM,
                'param',
                false
            );
            $and .= " AND c.id {$insql} ";
            $params = array_merge($params, $inparams);
        }

        return [$and, $params];
    }

    /**
     * Verifica se o usuário pertence a pelo menos um dos públicos-alvo da oferta.
     *
     * @param int $userid ID do usuário a verificar
     * @return bool true se pertence a algum público, false caso contrário
     */
    public function user_belongs_to_audiences(int $userid): bool
    {
        global $DB;

        $audienceids = $this->get_audience_ids();

        if (empty($audienceids)) {
            return false;
        }

        $params = [$userid];

        list($in_sql, $inparams) = $DB->get_in_or_equal($audienceids);

        $params = [...$params, ...$inparams];

        $sql = "SELECT 1
              FROM {local_audience_members}
             WHERE userid = ?
               AND audienceid $in_sql";

        return $DB->record_exists_sql($sql, $params);
    }

    public function get_classes_with_disabled_enrol_instances(): array
    {
        $classes = [];
        $offer_courses = $this->get_courses();

        if (!$offer_courses) {
            return $classes;
        }

        foreach ($offer_courses as $offer_course) {
            $disabled_classes = $offer_course->get_classes_with_disabled_enrol_instances();
            if($disabled_classes){
                $classes += $offer_course->get_classes_with_disabled_enrol_instances();
            }
        }

        return $classes;
    }

    public function enable_enrol_instances()
    {
        $classes = $this->get_classes_with_disabled_enrol_instances();
        
        if (!$classes) {
            return true;
        }

        foreach ($classes as $class) {
            $class->enable_enrol_instance();
        }
    }
}
