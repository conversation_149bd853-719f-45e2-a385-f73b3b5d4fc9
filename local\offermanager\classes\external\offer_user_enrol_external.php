<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_value;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\constants;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Offer User Enrol External API
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_user_enrol_external extends external_api
{

    /**
     * Define os parâmetros do método get_situation_list.
     *
     * @return external_function_parameters
     */
    public static function get_situation_list_parameters(): external_function_parameters
    {
        return new external_function_parameters([]);
    }

    /**
     * Retorna a lista de situações de inscrição possíveis.
     *
     * @return array
     */
    public static function get_situation_list(): array
    {

        self::validate_parameters(self::get_situation_list_parameters(), []);

        $situations = constants::get_situation_list();

        $result = [];
        foreach ($situations as $id => $name) {
            $result[] = ['id' => $id, 'name' => $name];
        }
        return $result;
    }

    /**
     * Define a estrutura de retorno do método get_situation_list.
     *
     * @return external_multiple_structure
     */
    public static function get_situation_list_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'Id da situação'),
                    'name' => new external_value(PARAM_TEXT, 'Descrição da situação'),
                ]
            ),
            'Lista de situações de inscrição possíveis.'
        );
    }

    /**
     * Define os parâmetros do método edit_enrolment.
     *
     * @return external_function_parameters
     */
    public static function edit_enrolment_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                'status' => new external_value(PARAM_INT, 'Status da inscrição'),
                'timestart' => new external_value(PARAM_INT, 'Data de início da inscrição'),
                'timeend' => new external_value(PARAM_INT, 'Data de término da inscrição'),
            ]
        );
    }

    /**
     * Edita a inscrição de um usuário em uma oferta.
     *
     * @param int $offeruserenrolid
     * @param int $status
     * @param int $timestart
     * @param int $timeend
     * @return bool
     */
    public static function edit_enrolment($offeruserenrolid, $status, $timestart, $timeend): bool
    {
        $params = self::validate_parameters(self::edit_enrolment_parameters(), [
            'offeruserenrolid' => $offeruserenrolid,
            'status' => $status,
            'timestart' => $timestart,
            'timeend' => $timeend,
        ]);

        $offer_user_enrol = offer_user_enrol_model::get_record(
            [
                'id' => $params['offeruserenrolid']
            ]
        );

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        return $offer_user_enrol->edit_enrolment($params['status'], $params['timestart'], $params['timeend']);
    }

    /**
     * Define a estrutura de retorno do método edit_enrolment.
     *
     * @return external_value
     */
    public static function edit_enrolment_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Indica se a edição da inscrição foi bem-sucedida');
    }

    /**
     * Define os parâmetros do método edit_enrolment_bulk.
     *
     * @return external_function_parameters
     */
    public static function edit_enrolment_bulk_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolids' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                    'Array de IDs das inscrições'
                ),
                'status' => new external_value(PARAM_INT, 'Status da inscrição'),
                'timestart' => new external_value(PARAM_INT, 'Data de início da inscrição'),
                'timeend' => new external_value(PARAM_INT, 'Data de término da inscrição'),
            ]
        );
    }

    /**
     * Edita em massa as inscrições de usuários em ofertas.
     *
     * @param array $offeruserenrolids
     * @param int $status
     * @param int $timestart
     * @param int $timeend
     * @return array
     */
    public static function edit_enrolment_bulk($offeruserenrolids, $status, $timestart, $timeend): array
    {
        $params = self::validate_parameters(
            self::edit_enrolment_bulk_parameters(),
            [
                'offeruserenrolids' => $offeruserenrolids,
                'status' => $status,
                'timestart' => $timestart,
                'timeend' => $timeend,
            ]
        );

        $results = [];

        foreach ($params['offeruserenrolids'] as $id) {

            $return = [
                'id' => $id
            ];

            $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $id]);

            $return['operation_status'] = $offer_user_enrol
                ? $offer_user_enrol->edit_enrolment($params['status'], $params['timestart'], $params['timeend'])
                : false
            ;

            $results[] = $return;
        }

        return $results;
    }

    /**
     * Define a estrutura de retorno do método edit_enrolment_bulk.
     *
     * @return external_multiple_structure
     */
    public static function edit_enrolment_bulk_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'offer user enrol id'),
                'operation_status' => new external_value(PARAM_BOOL, 'Resultado da operação para cada inscrição')
            ])
        );
    }
    /**
     * Define os parâmetros do método delete_enrolment.
     *
     * @return external_function_parameters
     */
    public static function delete_enrolment_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
            ]
        );
    }

    /**
     * Exclui a inscrição de um usuário em uma oferta.
     *
     * @param int $offeruserenrolid
     * @return bool
     */
    public static function delete_enrolment(int $offeruserenrolid): bool
    {
        $params = self::validate_parameters(
            self::delete_enrolment_parameters(),
            [
                'offeruserenrolid' => $offeruserenrolid,
            ]
        );

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $params['offeruserenrolid']]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        // O método delete() retorna o ID da matrícula excluída, mas precisamos retornar um booleano
        $result = $offer_user_enrol->delete();

        // Converter o resultado para booleano
        return !empty($result);
    }

    /**
     * Define a estrutura de retorno do método delete_enrolment.
     *
     * @return external_value
     */
    public static function delete_enrolment_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Indica se a exclusão da inscrição foi bem-sucedida');
    }

    /**
     * Define os parâmetros do método edit_enrolment_bulk.
     *
     * @return external_function_parameters
     */
    public static function delete_enrolment_bulk_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolids' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                    'Array de IDs das inscrições'
                )
            ]
        );
    }

    /**
     * Exclui em massa as inscrições de usuários em ofertas.
     *
     * @param array $offeruserenrolids
     * @return array
     */
    public static function delete_enrolment_bulk(array $offeruserenrolids): array
    {
        $params = self::validate_parameters(
            self::delete_enrolment_bulk_parameters(),
            [
                'offeruserenrolids' => $offeruserenrolids,
            ]
        );

        $results = [];

        foreach ($params['offeruserenrolids'] as $id) {
            $return = [
                'id' => $id
            ];

            $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $id]);

            $return['operation_status'] = $offer_user_enrol
                ? !empty($offer_user_enrol->delete())
                : false
            ;

            $results[] = $return;
        }

        return $results;
    }

    /**
     * Define a estrutura de retorno do método delete_enrolment_bulk.
     *
     * @return external_multiple_structure
     */
    public static function delete_enrolment_bulk_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'offer user enrol id'),
                'operation_status' => new external_value(PARAM_BOOL, 'Resultado da operação para cada inscrição')
            ])
        );
    }

    /**
     * Define os parâmetros do método get_roles.
     *
     * @return external_function_parameters
     */
    public static function get_roles_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
            ]
        );
    }

    /**
     * Retorna os papéis formatados para a inscrição do usuário na oferta.
     *
     * @param int $offeruserenrolid
     * @return array
     */
    public static function get_roles(int $offeruserenrolid): array
    {
        $params = self::validate_parameters(self::get_roles_parameters(), [
            'offeruserenrolid' => $offeruserenrolid,
        ]);

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $params['offeruserenrolid']]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        return $offer_user_enrol->get_formated_roles();
    }

    /**
     * Define a estrutura de retorno do método get_roles.
     *
     * @return external_multiple_structure
     */
    public static function get_roles_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'ID do papel'),
                    'name' => new external_value(PARAM_TEXT, 'Nome do papel'),
                ]
            ),
            'Lista de papéis formatados para a inscrição do usuário na oferta.'
        );
    }

    /**
     * Define os parâmetros do método update_roles.
     *
     * @return external_function_parameters
     */
    public static function update_roles_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                'roleids' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID do papel'),
                    'Array de IDs dos papéis'
                ),
            ]
        );
    }

    /**
     * Atualiza os papéis de um usuário em uma oferta.
     *
     * @param int $offeruserenrolid
     * @param array $roleids
     * @return bool
     */
    public static function update_roles(int $offeruserenrolid, array $roleids): bool
    {
        $params = self::validate_parameters(self::update_roles_parameters(), [
            'offeruserenrolid' => $offeruserenrolid,
            'roleids' => $roleids,
        ]);

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $params['offeruserenrolid']]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        return $offer_user_enrol->update_roles($params['roleids']);
    }

    /**
     * Define a estrutura de retorno do método update_roles.
     *
     * @return external_value
     */
    public static function update_roles_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Indica se a atualização dos papéis foi bem-sucedida');
    }
}
