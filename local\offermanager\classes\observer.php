<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager;

use core\event\base as base_event;
use core\task\manager as task_manager;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\task\update_operational_cycle_on_class_starttime;
use local_offermanager\task\update_operational_cycle_on_class_endtime;
use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\constants;
use local_offermanager\enrol_setup;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Observer class for offer manager events.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class observer
{

    /**
     * Handles the enrol_instance_created event.
     *
     * @param \core\event\enrol_instance_created $event The event object.
     * @return bool
     */
    public static function enrol_instance_created(\core\event\enrol_instance_created $event): bool
    {
        return self::schedule_operational_cycle_update($event);
    }

    /**
     * Handles the enrol_instance_updated event.
     *
     * @param \core\event\enrol_instance_updated $event The event object.
     * @return bool
     */
    public static function enrol_instance_updated(\core\event\enrol_instance_updated $event): bool
    {
        return self::schedule_operational_cycle_update($event);
    }

    /**
     * Handles the enrol_instance_deleted event.
     *
     * @param \core\event\enrol_instance_deleted $event The event object.
     * @return bool
     */
    public static function enrol_instance_deleted(\core\event\enrol_instance_deleted $event): bool
    {
        global $DB;

        $enrolid = $event->objectid;
        $enrolinstance = $DB->get_record('enrol', ['id' => $enrolid]);

        if (!$enrolinstance) {
            return true;
        }

        $offer_enrol_plugins = enrol_setup::get_dependent_enrol_plugins();

        if (!in_array($enrolinstance->enrol, $offer_enrol_plugins)) {
            return true;
        }

        $offerclass = offer_class_model::get_by_enrolid($enrolid);
        if (!$offerclass) {
            return true;
        }

        $offerclassid = $offerclass->get('id');

        // Cancel scheduled tasks.
        $DB->delete_records(
            'task_adhoc',
            [
                'component' => 'local_offermanager',
                'customdata' => json_encode(['offerclassid' => $offerclassid])
            ]
        );

        return true;
    }

    /**
     * Schedules adhoc tasks to update the operational cycle based on enrol dates.
     *
     * @param base_event $event The event object (\core\event\enrol_instance_created or \core\event\enrol_instance_updated).
     * @return bool True if tasks were scheduled or not applicable, false on error.
     */
    public static function schedule_operational_cycle_update(base_event $event): bool
    {
        global $DB;

        $enrolid = $event->objectid;
        $userid = $event->userid;
        $enrolinstance = $DB->get_record('enrol', ['id' => $enrolid]);

        if (!$enrolinstance) {
            return true;
        }

        $offerclass = offer_class_model::get_by_enrolid($enrolid);
        if (!$offerclass) {
            return true;
        }

        if ($offerclass->is_finished()) {
            return true;
        }

        $offerclassid = $offerclass->get('id');

        $offer_enrol_plugins = enrol_setup::get_dependent_enrol_plugins();

        if (!in_array($enrolinstance->enrol, $offer_enrol_plugins)) {
            return true;
        }

        $offerclassid = $offerclass->get('id');
        $starttime = $offerclass->get_mapped_field('startdate');
        $enableenddate = $offerclass->get_mapped_field('enableenddate');
        $endtime = $enableenddate ? $offerclass->get_mapped_field('enddate') : null;
        $currenttime = time();
        $delay = 10;

        $customdata = new \stdClass();
        $customdata->offerclassid = $offerclassid;

        $operational_cycle_updated = $offerclass->update_operational_cycle();

        if ($operational_cycle_updated) {
            $offerclass->update_accessability();
        }

        // Schedule task for start time if it's in the future.
        if ($starttime > 0 && $starttime > $currenttime) {
            $starttask = new update_operational_cycle_on_class_starttime();

            $starttask->set_custom_data([
                'offerclassid' => $offerclassid
            ]);

            $starttask->set_component('local_offermanager');

            $starttask->set_userid($userid);

            $starttask->set_next_run_time($starttime + $delay);

            task_manager::reschedule_or_queue_adhoc_task($starttask);
        }

        // Schedule task for end time if it's in the future.
        if ($endtime > 0 && $endtime > $currenttime) {

            $endtask = new update_operational_cycle_on_class_endtime();

            $endtask->set_custom_data([
                'offerclassid' => $offerclassid
            ]);

            $endtask->set_component('local_offermanager');

            $endtask->set_userid($userid);

            $endtask->set_next_run_time($endtime + $delay);

            task_manager::reschedule_or_queue_adhoc_task($endtask);
        }

        return true;
    }

    /**
     * Atualiza o status da matrícula de 'enroled' para 'in_progress' quando necessário.
     *
     * @param \core\event\course_module_completion_updated $event
     * @return bool True if the situation was successfully updated.
     */
    public static function update_enrol_situation_on_module_completion(\core\event\course_module_completion_updated $event): bool
    {
        $userid = $event->relateduserid;
        $courseid = $event->courseid;
        $offer_user_enrolments = offer_user_enrol_model::get_offer_enrolments_by_user_and_course(
            $userid,
            $courseid,
            constants::OFFER_USER_ENROL_SITUATION_ENROLED
        );

        if (!$offer_user_enrolments) {
            return false;
        }

        $updated = false;

        foreach ($offer_user_enrolments as $offer_user_enrolment) {
            try {
                $offer_user_enrolment->update_grade_and_progress();
                if($offer_user_enrolment->is_enroled()) {
                    $return = $offer_user_enrolment->update_situation(constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS);
                    $updated = $updated || $return;
                }

            } catch (moodle_exception $e) {
            }
        }

        return $updated;
    }

    /**
     * Handles the course_completed event.
     * Updates the user enrolment situation based on course completion criteria.
     *
     * @param \core\event\course_completed $event The event object.
     * @return bool True if any enrolment situation was updated, false otherwise.
     */
    public static function update_situations_on_course_completion(\core\event\course_completed $event): bool
    {
        $userid = $event->relateduserid;
        $courseid = $event->courseid;

        $offer_user_enrolments = offer_user_enrol_model::get_offer_enrolments_by_user_and_course(
            $userid,
            $courseid,
            [
                constants::OFFER_USER_ENROL_SITUATION_ENROLED,
                constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS
            ]
        );

        if (!$offer_user_enrolments) {
            return false;
        }

        sleep(1);
        $updated = false;

        foreach ($offer_user_enrolments as $offer_user_enrolment) {
            try {
                
                $new_situation = $offer_user_enrolment->course_has_modules_with_completion_by_grade()
                    ? constants::OFFER_USER_ENROL_SITUATION_APPROVED
                    : constants::OFFER_USER_ENROL_SITUATION_COMPLETED
                ;
                
                $offer_user_enrolment->update_grade_and_progress();
                $updated = $offer_user_enrolment->update_situation($new_situation);

            } catch (moodle_exception $e) {
            }
        }

        return $updated;
    }

    /**
     * Updates class accessibility when a user enrolment is created.
     *
     * @param \local_offermanager\event\offer_user_enrol_created $event The event object.
     * @return bool True if the class accessibility was updated or not applicable.
     */
    public static function update_class_accessibility_on_enrol_created(\local_offermanager\event\offer_user_enrol_created $event): bool
    {
        $offerclassid = $event->other['offerclassid'];
        $offerclass = offer_class_model::get_record(['id' => $offerclassid]);

        if (!$offerclass) {
            return true;
        }

        if (!$offerclass->is_accessible() && $offerclass->check_if_accessible()) {
            $offerclass->set_accessible();
        }

        return true;
    }

    /**
     * Handles the offer_user_enrol_deleted event.
     *
     * @param \local_offermanager\event\offer_user_enrol_deleted $event The event object.
     * @return bool True if the user was successfully removed from the teacher list.
     */
    public static function offer_user_enrol_deleted(\local_offermanager\event\offer_user_enrol_deleted $event): bool
    {
        global $DB;

        $enrolid = $event->data['other']['enrolid'];
        $userid = $event->relateduserid;

        $enrol = $DB->get_record('enrol', ['id' => $enrolid], 'customtext2');
        
        if (!$enrol) {
            return true;
        }

        $teacherids = $enrol->customtext2
            ? explode(', ', $enrol->customtext2)
            : []
        ;

        if (($key = array_search($userid, $teacherids)) !== false) {
            unset($teacherids[$key]);

            $DB->set_field(
                'enrol',
                'customtext2',
                implode(', ', $teacherids),
                ['id' => $enrolid]
            );
        }

        return true;
    }
}
