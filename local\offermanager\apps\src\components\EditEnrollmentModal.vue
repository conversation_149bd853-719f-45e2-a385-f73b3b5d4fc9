<template>
  <div v-if="show" class="modal-backdrop" @click="$emit('close')">
    <div class="modal-container" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">
          Editar matrícula de {{ user ? user.fullName : "" }}
        </h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body">
        <div class="enrollment-form">
          <!-- Método de inscrição (somente leitura) -->
          <div class="form-row">
            <div class="form-label">Método de inscrição</div>
            <div class="form-value">
              {{ getEnrolmentMethod(user && user.enrol ? user.enrol : "") }}
            </div>
          </div>

          <!-- Estado (ativo/inativo) -->
          <div class="form-row">
            <div class="form-label">Estado</div>
            <div class="form-field">
              <div class="select-wrapper">
                <CustomSelect
                  v-model="formData.status"
                  :options="statusOptions"
                  :width="120"
                  class="smaller-select"
                />
              </div>
            </div>
          </div>

          <!-- Data de início da matrícula -->
          <div class="form-row">
            <div class="form-label">Matrícula começa</div>
            <div class="form-field date-time-field">
              <div class="date-field">
                <input
                  type="date"
                  v-model="formData.startDateStr"
                  class="form-control"
                  @change="handleStartDateChange"
                />
              </div>
              <div class="time-field">
                <input
                  type="time"
                  v-model="formData.startTimeStr"
                  class="form-control"
                  @change="handleStartTimeChange"
                />
              </div>
              <div class="enable-checkbox">
                <input
                  type="checkbox"
                  id="enable-start-date"
                  v-model="formData.enableStartDate"
                  class="custom-checkbox"
                />
                <label for="enable-start-date">Habilitar</label>
              </div>
            </div>
          </div>

          <!-- Período de validade da matrícula (movido para cima) -->
          <div class="form-row">
            <div class="form-label">Período de validade da matrícula</div>
            <div class="form-field">
              <div class="select-wrapper">
                <CustomSelect
                  v-model="formData.validityPeriod"
                  :options="validityPeriodOptions"
                  :width="120"
                  class="smaller-select"
                  @change="handleValidityPeriodChange"
                  :disabled="formData.enableEndDate"
                />
              </div>
            </div>
          </div>

          <!-- Matrícula termina -->
          <div class="form-row">
            <div class="form-label">Matrícula termina</div>
            <div
              class="form-field date-time-field"
              :class="{ 'disabled-inputs-only': !formData.enableEndDate }"
            >
              <div class="date-field">
                <input
                  type="date"
                  v-model="formData.endDateStr"
                  class="form-control"
                  :disabled="!formData.enableEndDate"
                />
              </div>
              <div class="time-field">
                <input
                  type="time"
                  v-model="formData.endTimeStr"
                  class="form-control"
                  :disabled="!formData.enableEndDate"
                />
              </div>
              <div class="enable-checkbox">
                <input
                  type="checkbox"
                  id="enable-enddate"
                  v-model="formData.enableEndDate"
                  class="custom-checkbox"
                  @change="handleEnableEndDateChange"
                />
                <label for="enable-enddate">Habilitar</label>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-label">Matrícula criada</div>
            <div class="form-value">
              {{
                user && user.createdDate ? user.createdDate : "Não disponível"
              }}
            </div>
          </div>
        </div>
      </div>

      <!-- Rodapé do modal -->
      <div class="modal-footer">
        <div class="footer-spacer"></div>
        <div class="footer-buttons">
          <button
            class="btn btn-primary"
            @click="saveChanges"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? "Salvando..." : "Salvar mudanças" }}
          </button>
          <button class="btn btn-secondary" @click="$emit('close')">
            Cancelar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomSelect from "./CustomSelect.vue";
import { editEnrolment } from "@/services/enrolment";

export default {
  name: "EditEnrollmentModal",
  components: {
    CustomSelect,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: null,
    },
    offerclassid: {
      type: [Number, String],
      required: true,
    },
  },
  emits: ["close", "success", "error"],
  data() {
    return {
      isSubmitting: false,
      formData: {
        status: "1",

        enableStartDate: true,
        startDateStr: "",
        startTimeStr: "00:00",

        enableEndDate: false,
        endDateStr: "",
        endTimeStr: "00:00",

        validityPeriod: "unlimited", // Novo campo para período de validade
      },
      statusOptions: [
        { value: 0, label: "Ativo" },
        { value: 1, label: "Suspenso" },
      ],
      // Opções para o período de validade da matrícula
      validityPeriodOptions: [
        { value: "unlimited", label: "Ilimitado" },
        ...Array.from({ length: 365 }, (_, i) => {
          const days = i + 1;
          return {
            value: days.toString(),
            label: days === 1 ? "1 dia" : `${days} dias`,
          };
        }),
      ],
    };
  },

  watch: {
    show(newVal) {
      if (newVal && this.user) {
        this.initializeForm();
      }
    },
    user(newVal) {
      if (newVal && this.show) {
        this.initializeForm();
      }
    },
  },

  methods: {
    getEnrolmentMethod(enrol) {
      if (!enrol) return "Não disponível";

      switch (enrol) {
        case "offer_manual":
          return "Inscrição manual";
        case "offer_self":
          return "Autoinscrição";
        default:
          return enrol;
      }
    },

    initializeForm() {
      if (!this.user) return;

      // 1) Status
      this.formData.status = this.user.state;

      // 2) Start Date
      const startTs = this.user.timestart;
      const startRef = startTs ? new Date(startTs * 1000) : new Date();
      this.formData.startDateStr = this.formatDateForInput(startRef);
      this.formData.startTimeStr = this.formatTimeForInput(startRef);
      this.formData.enableStartDate = true;

      // 3) End Date / Validity
      const validityOpts = this.validityPeriodOptions.filter(
        (o) => o.value !== "unlimited"
      );

      if (this.user.timeend) {
        const endRef = new Date(this.user.timeend * 1000);
        this.formData.endDateStr = this.formatDateForInput(endRef);
        this.formData.endTimeStr = this.formatTimeForInput(endRef);
        this.formData.enableEndDate = this.user.timeend > 0;

        // calcula dias entre start e end
        const startDate = startRef;
        const diffMs = endRef - startDate;
        const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

        // tenta achar opção correspondente
        const match = validityOpts.find((o) => parseInt(o.value) === diffDays);
        this.formData.validityPeriod = match ? match.value : "unlimited";
      } else {
        // default: 3 meses + validade ilimitada
        const future = new Date();
        future.setMonth(future.getMonth() + 3);

        this.formData.endDateStr = this.formatDateForInput(future);
        this.formData.endTimeStr = this.formatTimeForInput(future);
        this.formData.validityPeriod = "unlimited";
        this.formData.enableEndDate = false;
      }
    },

    // Método para lidar com a mudança no período de validade
    handleValidityPeriodChange() {
      // Se um período específico for selecionado, desmarcar o checkbox de habilitação
      if (this.formData.validityPeriod !== "unlimited") {
        this.formData.enableEndDate = false;

        // Calcular a nova data de término com base no período selecionado
        const startDate =
          this.formData.enableStartDate && this.formData.startDateStr
            ? new Date(this.formData.startDateStr)
            : new Date();

        const days = parseInt(this.formData.validityPeriod);
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + days);

        // Atualizar os campos de data e hora de validade
        this.formData.endDateStr = this.formatDateForInput(endDate);
        this.formData.endTimeStr = this.formData.startTimeStr;
      }
    },

    handleEnableEndDateChange() {
      if (this.formData.enableEndDate) {
        this.formData.validityPeriod = "unlimited";
      }
    },

    formatDateForInput(date) {
      return date.toISOString().split("T")[0];
    },
    formatTimeForInput(date) {
      return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },

    async saveChanges() {
      if (!this.user?.offeruserenrolid) return;

      try {
        this.isSubmitting = true;

        const status = Number(this.formData.status) || 0;
        const timestart = this.getStartTimestamp();
        const timeend = this.getEndTimestamp(timestart);

        if (timestart > timeend && timeend !== 0) {
          this.$emit(
            "error",
            "A data de início da matrícula deve ser menor que a data de fim da matrícula."
          );
          return;
        }

        const response = await editEnrolment({
          offeruserenrolid: this.user.offeruserenrolid,
          status,
          timestart,
          timeend,
        });

        if (response) {
          this.$emit("success", {
            userId: this.user.id,
            offeruserenrolid: this.user.offeruserenrolid,
            status,
            timestart,
            timeend,
          });
          this.$emit("close");
        } else {
          this.$emit(
            "error",
            "Não foi possível editar a matrícula. Por favor, tente novamente."
          );
        }
      } catch (error) {
        this.$emit(
          "error",
          "Ocorreu um erro ao editar a matrícula. Por favor, tente novamente."
        );
      } finally {
        this.isSubmitting = false;
      }
    },

    getStartTimestamp() {
      if (this.formData.enableStartDate && this.formData.startDateStr) {
        const startDate = this.parseDateTime(
          this.formData.startDateStr,
          this.formData.startTimeStr
        );
        return Math.floor(startDate.getTime() / 1000);
      }
      return 0;
    },

    getEndTimestamp(timestart) {
      if (this.formData.enableEndDate && this.formData.endDateStr) {
        const endDate = this.parseDateTime(
          this.formData.endDateStr,
          this.formData.endTimeStr
        );
        return Math.floor(endDate.getTime() / 1000);
      }

      if (this.formData.validityPeriod !== "unlimited") {
        const days = parseInt(this.formData.validityPeriod);
        if (this.formData.enableStartDate && this.formData.startDateStr) {
          const startDate = this.parseDateTime(
            this.formData.startDateStr,
            this.formData.startTimeStr
          );
          const endDate = new Date(startDate);
          endDate.setDate(endDate.getDate() + days);
          return Math.floor(endDate.getTime() / 1000);
        }
      }

      return 0;
    },

    parseDateTime(dateStr, timeStr) {
      const [year, month, day] = dateStr.split("-").map(Number);
      const [hours, minutes] = timeStr.split(":").map(Number);
      return new Date(year, month - 1, day, hours, minutes, 0, 0);
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 700px;
  border: 1px solid #373b3e;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #373b3e;
  background-color: #212529;
  position: relative;

  .modal-title {
    margin: 0;
    font-size: 18.75px;
    font-weight: bold;
    color: #f8f9fa;
  }

  .modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);

    i {
      font-size: 0.9rem;
    }

    &:hover {
      color: #ffffff;
      opacity: 0.8;
    }
  }
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-top: 1px solid #373b3e;
  background-color: #212529;

  .footer-spacer {
    flex: 1;
  }

  .footer-buttons {
    display: flex;
    gap: 10px;
  }

  .btn {
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .btn-primary {
    font-size: 15px;

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }

  .btn-secondary {
    color: #000;
  }
}

.enrollment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #f8f9fa;
  font-size: 15px;
  min-width: 220px;
  flex-shrink: 0;
}

.form-value {
  color: #f8f9fa;
  font-size: 15px;
  flex: 1;
}

.form-field {
  flex: 1;

  /* Sobrescrever o estilo global dos selects */
  :deep(.custom-select) {
    width: auto !important;
  }
}

.date-time-field {
  display: flex;
  gap: 10px;
  align-items: center;
}

.date-field {
  position: relative;
  flex: 2;

  input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    background-color: #212529;
    border: 1px solid #373b3e;
    border-radius: 4px;
    color: #fff;
    font-size: 0.875rem;
  }

  /* Estilo para o input de data */
  input[type="date"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px; /* Espaço para o ícone de calendário nativo */
    position: relative;
    cursor: pointer;
  }

  /* Estilo para o ícone de calendário nativo */
  input[type="date"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    color: var(--primary);
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
  }
}

.time-field {
  position: relative;
  flex: 1;

  input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    background-color: #212529;
    border: 1px solid #373b3e;
    border-radius: 4px;
    color: #fff;
    font-size: 0.875rem;
  }

  /* Estilo para o input de hora */
  input[type="time"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px; /* Espaço para o ícone de relógio nativo */
    position: relative;
    cursor: pointer;
  }

  /* Estilo para o ícone de relógio nativo */
  input[type="time"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    color: var(--primary);
    cursor: pointer;
    height: 20px;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
  }
}

.enable-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  width: 100px;

  input[type="checkbox"] {
    margin: 0;
  }

  label {
    color: #fff;
    font-size: 0.875rem;
    margin: 0;
  }
}

.custom-checkbox {
  width: 20px;
  height: 20px;
  background-color: #212529;
  border: 1px solid #373b3e;
  border-radius: 3px;
  appearance: none;
  cursor: pointer;
  position: relative;

  &:checked {
    background-color: var(--primary);
    border-color: var(--primary);

    &::after {
      content: "";
      position: absolute;
      left: 7px;
      top: 3px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }
}

/* Wrapper para os selects */
.select-wrapper {
  width: 120px !important;
  position: relative !important;
  display: inline-block !important;

  :deep(.custom-select) {
    width: 120px !important;
    max-width: 120px !important;
  }

  :deep(.select-container) {
    width: 120px !important;
    max-width: 120px !important;
    height: 28px !important;
    overflow: hidden !important;
    position: relative !important;
  }

  :deep(.select-value) {
    max-width: 90px !important; /* Deixa espaço para a setinha */
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    display: inline-block !important;
  }

  :deep(.select-arrow) {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 16px !important;
    height: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 2 !important;
  }

  /* Sobrescrever o estilo global que pode estar causando o problema */
  :deep([data-v-944e90b]) {
    width: 120px !important;
  }
}

/* Estilo para selects menores */
.smaller-select {
  :deep(.select-container) {
    height: 28px !important;
    font-size: 12px !important;
    background-color: #212529 !important;
    border: 1px solid #373b3e !important;
    border-radius: 4px !important;
    color: #fff !important;
  }

  :deep(.select-value) {
    padding: 0 6px !important;
    font-size: 12px !important;
  }

  :deep(.select-dropdown) {
    font-size: 12px !important;
    max-width: 120px !important;
    width: 120px !important;
    background-color: #212529 !important;
    border: 1px solid #373b3e !important;
  }

  :deep(.select-option) {
    padding: 5px 6px !important;
    color: #fff !important;

    &:hover,
    &.selected {
      background-color: var(--primary) !important;
    }
  }
}

/* Estilo para campos desabilitados */
.disabled-field {
  opacity: 0.6;
  pointer-events: none;
}

/* Estilo para desabilitar apenas os inputs, não o checkbox */
.disabled-inputs-only {
  .date-field,
  .time-field {
    opacity: 0.6;
    pointer-events: none;
  }

  .enable-checkbox {
    opacity: 1;
    pointer-events: auto;
  }
}
</style>
