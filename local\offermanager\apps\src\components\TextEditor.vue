<template>
  <div class="text-editor-container">
    <label v-if="label" class="filter-label" :class="{ 'disabled': disabled }">{{ label }}</label>
    <div class="editor-container" :class="{ 'disabled': disabled }">
      <div class="editor-toolbar">
        <div class="toolbar-group">
          <button class="btn-editor" @click="!disabled && applyFormat('bold')" title="Negrito" :disabled="disabled">
            <i class="fas fa-bold"></i>
          </button>
          <button class="btn-editor" @click="!disabled && applyFormat('italic')" title="Itálico" :disabled="disabled">
            <i class="fas fa-italic"></i>
          </button>
          <button class="btn-editor" @click="!disabled && applyFormat('underline')" title="Sublinhado" :disabled="disabled">
            <i class="fas fa-underline"></i>
          </button>
          <button class="btn-editor" @click="!disabled && applyFormat('strikethrough')" title="Tachado" :disabled="disabled">
            <i class="fas fa-strikethrough"></i>
          </button>
        </div>

        <div class="toolbar-divider"></div>

        <div class="toolbar-group">
          <button class="btn-editor" @click="!disabled && applyFormat('insertUnorderedList')" title="Lista não ordenada" :disabled="disabled">
            <i class="fas fa-list-ul"></i>
          </button>
          <button class="btn-editor" @click="!disabled && applyFormat('insertOrderedList')" title="Lista ordenada" :disabled="disabled">
            <i class="fas fa-list-ol"></i>
          </button>
        </div>

        <!-- <div class="toolbar-divider"></div>

        <div class="toolbar-group">
          <button class="btn-editor" @click="!disabled && insertLink" title="Inserir link" :disabled="disabled">
            <i class="fas fa-link"></i>
          </button>
        </div>

        <div class="toolbar-divider"></div>

        <div class="toolbar-group">
          <button class="btn-editor" @click="!disabled && insertImage" title="Inserir imagem" :disabled="disabled">
            <i class="fas fa-image"></i>
          </button>
        </div>

        <div class="toolbar-divider"></div>

        <div class="toolbar-group">
          <button class="btn-editor" @click="!disabled && toggleHtmlView" title="Alternar visualização HTML" :disabled="disabled">
            <i class="fas fa-code"></i>
          </button>
        </div> -->
      </div>
      <div
        v-if="!showHtmlSource"
        class="editor-content"
        :contenteditable="!disabled"
        @input="updateContent"
        @keyup="updateContent"
        ref="editableContent"
      ></div>
      <textarea
        v-else
        v-model="htmlContent"
        class="editor-textarea"
        :rows="rows"
        :placeholder="placeholder"
        @input="updateHtmlContent"
        :disabled="disabled"
      ></textarea>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextEditor',

  props: {
    modelValue: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: 'Digite o conteúdo aqui...'
    },
    rows: {
      type: Number,
      default: 5
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:modelValue'],

  data() {
    return {
      showHtmlSource: false,
      htmlContent: this.modelValue
    }
  },

  mounted() {
    if (this.$refs.editableContent) {
      this.$refs.editableContent.textContent = this.modelValue;
    }
  },

  watch: {
    modelValue: {
      handler(newValue) {
        if (this.showHtmlSource) {
          this.htmlContent = newValue;
        } else if (this.$refs.editableContent && this.$refs.editableContent.textContent !== newValue) {
          this.$refs.editableContent.textContent = newValue;
        }
      },
      immediate: true
    }
  },

  methods: {
    applyFormat(command, value = null) {
      if (this.showHtmlSource) return;

      document.execCommand(command, false, value);
      this.updateContent();
    },

    insertLink() {
      if (this.showHtmlSource) return;

      const url = prompt('Digite a URL do link:', 'http://');
      if (url) {
        this.applyFormat('createLink', url);
      }
    },

    insertImage() {
      if (this.showHtmlSource) return;

      const url = prompt('Digite a URL da imagem:', 'http://');
      if (url) {
        this.applyFormat('insertImage', url);
      }
    },

    toggleHtmlView() {
      if (!this.showHtmlSource) {
        // Mudando para visualização HTML
        this.htmlContent = this.$refs.editableContent.textContent;
      } else {
        // Voltando para visualização WYSIWYG
        this.$nextTick(() => {
          if (this.$refs.editableContent) {
            this.$refs.editableContent.textContent = this.htmlContent;
          }
        });
      }

      this.showHtmlSource = !this.showHtmlSource;
    },

    updateContent() {
      if (!this.showHtmlSource && this.$refs.editableContent) {
        const content = this.$refs.editableContent.textContent;
        this.$emit('update:modelValue', content);
      }
    },

    updateHtmlContent() {
      if (this.showHtmlSource) {
        this.$emit('update:modelValue', this.htmlContent);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-editor-container {
  margin-bottom: 1rem;
  max-width: 700px;
}

.filter-label {
  font-size: 14px;
  color: #fff;
  margin-bottom: 5px;
  display: block;

  &.disabled {
    opacity: 0.65;
  }
}

.editor-container {
  border: 1px solid #495057;
  border-radius: 4px;
  overflow: hidden;

  &.disabled {
    opacity: 0.65;
  }
}

.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 0.5rem;
  background-color: #2b3035;
  border-bottom: 1px solid #495057;
  align-items: center;

  .toolbar-group {
    display: flex;
    margin: 0;

    .btn-editor {
      border-radius: 0;
      margin: 0;
      border-right: none;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-right: solid 1px rgba(255, 255, 255, 0.1);
      }
    }
  }

  .toolbar-divider {
    width: 1px;
    height: 24px;
    background-color: #495057;
    margin: 0 0.5rem;
  }

  .btn-editor {
    background-color: #343A40;
    border: solid 1px rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;

    &:hover:not(:disabled) {
      background-color: #5a6268;
    }

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }
}

.editor-content {
  width: 100%;
  min-height: 150px;
  padding: 0.75rem;
  background-color: #212529;
  color: #fff;
  outline: none;
  overflow-y: auto;
  text-align: left;
  writing-mode: horizontal-tb;
  direction: ltr;

  &[contenteditable="false"] {
    cursor: not-allowed;
  }
}

.editor-textarea {
  width: 100%;
  padding: 0.75rem;
  border: none;
  background-color: #212529;
  color: #fff;
  resize: vertical;
  min-height: 150px;

  &:focus {
    outline: none;
  }

  &:disabled {
    cursor: not-allowed;
  }
}
</style>