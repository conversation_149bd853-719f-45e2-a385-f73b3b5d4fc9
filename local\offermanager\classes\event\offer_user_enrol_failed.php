<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\event;

use moodle_url;
use local_offermanager\persistent\offer_user_enrol_model;
use context_offer_class;

defined('MOODLE_INTERNAL') || die();

/**
 * Evento disparado quando uma inscrição em oferta muda para a situação 'reprovado'.
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_user_enrol_failed extends \core\event\base {

    /**
     * Inicializa as propriedades do evento.
     */
    protected function init(): void {
        $this->data['crud'] = 'u';
        $this->data['edulevel'] = self::LEVEL_PARTICIPATING;
        $this->data['objecttable'] = 'local_offermanager_ue';
    }

    /**
     * Retorna o nome do evento.
     *
     * @return string
     */
    public static function get_name(): string {
        // TODO: Add this string to the language file.
        return get_string('event:offer_user_enrol_failed', 'local_offermanager');
    }

    /**
     * Cria uma instância do evento.
     *
     * @param offer_user_enrol_model $offer_user_enrol The enrolment record.
     * @return self
     */
    public static function instance(offer_user_enrol_model $offer_user_enrol): self
    {
        global $USER;

        return self::create([
            'context' => context_offer_class::instance($offer_user_enrol->get('offerclassid')),
            'objectid' => $offer_user_enrol->get('id'),
            'relateduserid' => $offer_user_enrol->get('userid'),
            'userid' => $USER->id,
            'other' => [],
        ]);
    }

    /**
     * Retorna a descrição do evento.
     *
     * @return string
     */
    public function get_description(): string {
        // TODO: Improve description if needed, potentially using language strings.
        $desc = "A inscrição do usuário com ID {$this->relateduserid} ";
        $desc .= "na turma com ID {$this->objectid} foi atualizada para a situação 'reprovado' pelo ";
        $desc .= "usuário com ID {$this->userid}.";
        return $desc;
    }

    /**
     * Retorna a URL relacionada ao evento.
     *
     * @return moodle_url
     */
    public function get_url(): moodle_url {
        // TODO: Update URL if a more specific one exists.
        return new moodle_url('/local/offermanager/index.php');
    }
}