import { createRouter, createWebHistory } from "vue-router";
import OfferManagerView from "../views/OfferManagerView.vue";
import RegisteredUsers from "../views/RegisteredUsers.vue";
import NewOfferView from "../views/NewOfferView.vue";
import NewClassView from "../views/NewClassView.vue";

const routes = [
  {
    path: "/",
    name: "listar-ofertas",
    component: OfferManagerView,
    meta: {
      title: "Gerenciar Ofertas",
    },
  },
  {
    path: "/new-offer",
    name: "nova-oferta",
    component: NewOfferView,
    meta: {
      title: "Nova Oferta",
    },
  },
  {
    path: "/edit-offer/:id",
    name: "editar-oferta",
    component: NewOfferView,
    props: true,
    meta: {
      title: "Editar Oferta",
    },
  },
  {
    path: "/new-class/:offercourseid/:offerid",
    name: "NewClass",
    component: NewClassView,
    props: true,
    meta: {
      title: "Nova Turma",
    },
  },
  {
    path: "/edit-class/:offercourseid/:classid/:offerid",
    name: "EditClass",
    component: NewClassView,
    props: true,
    meta: {
      title: "Editar Turma",
    },
  },
  {
    path: "/new-subscribed-users/:offerclassid",
    name: "usuarios-matriculados",
    component: RegisteredUsers,
    props: true,
    meta: {
      title: "Usuários matriculados",
    },
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/",
  },
];

const router = createRouter({
  history: createWebHistory("/local/offermanager/"),
  routes,
  // Configuração para rolar para o topo da página quando uma nova rota é carregada
  scrollBehavior() {
    // Sempre rola para o topo
    return { top: 0 };
  },
});

// Atualiza o título da página quando a rota muda
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || "Gerenciar Ofertas";
  next();
});

// Tratamento de erros de navegação
router.onError((error) => {
  console.error("Erro de navegação:", error);

  // Se o erro for relacionado a uma rota não encontrada, redirecionar para a página inicial
  if (
    error.name === "NavigationDuplicated" ||
    error.message.includes("No match") ||
    error.message.includes("missing required param")
  ) {
    router.push("/");
  }
});

export default router;
