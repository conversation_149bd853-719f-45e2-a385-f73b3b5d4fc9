<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use local_offermanager\constants;
use course_enrolment_manager;
use local_offermanager\persistent\trait\user_enrolment_course_trait;
use local_offermanager\persistent\trait\user_enrolment_situations_trait;
use local_offermanager\persistent\trait\user_enrolment_extension_trait;
use local_offermanager\persistent\trait\user_enrolment_reenrolment_trait;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\persistent\offer_user_enrol_history_model;
use local_offermanager\persistent\trait\user_enrolment_class_roles_trait;
use local_offermanager\event\offer_user_enrol_created;
use local_offermanager\event\offer_user_enrol_edited;
use local_offermanager\event\offer_user_enrol_deleted;
use core\session\manager;
use stdClass;
use cm_info;
use context_offer_class;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/locallib.php');

/**
 * Represents a record in the local_offermanager_user_enrol table.
 *
 * This class models the relationship between an offer class (local_offermanager_class)
 * and a user's enrolment (user_enrolments) within that class context.
 *
 * @package    local_offermanager
 * @copyright  2025 Your Name/Organisation
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 *
 * @property int $id The unique ID of this record.
 * @property int $offerclassid The ID of the related offer class (local_offermanager_class).
 * @property int $ueid The ID of the user enrolment record (user_enrolments).
 * @property int $userid The ID of the enrolled user (user).
 * @property int $courseid The ID of the course associated with the enrolment (course).
 * @property int $situation The status/situation of this enrolment relation (uses constants from local_offermanager\constants).
 * @property int $progress Progresso do usuário no curso (0-100%).
 * @property float|null $grade Nota final do usuário no curso.
 * @property int $extensioncount Número de vezes que o usuário prorrogou a inscrição.
 * @property int $history Indica se a matrícula foi movida para o histórico (0=não, 1=sim).
 * @property int|null $userhistory ID do usuário que moveu a matrícula para o histórico.
 * @property int|null $timehistory UNIX Timestamp quando a matrícula foi movida para o histórico.
 */
class offer_user_enrol_model extends persistent
{
    use user_enrolment_situations_trait;
    use user_enrolment_course_trait;
    use user_enrolment_extension_trait;
    use user_enrolment_reenrolment_trait;
    use user_enrolment_class_roles_trait;

    private $userid;

    /** @var string The name of the database table this persistent maps to. */
    const TABLE = 'local_offermanager_ue';

    /** @var stdClass|bool|null The user enrolment. */
    private stdClass|bool|null $user_enrolment = null; // User enrolment

    /** @var ?offer_class_model The offer class model. */
    public ?offer_class_model $offerclass = null;

    /** @var cm_info|bool The offer class model. */
    public cm_info|bool $attendance_cm = false;

    /**
     * Define the properties of this persistent class.
     *
     * @return array The property definitions.
     */
    protected static function define_properties()
    {
        return [
            'offerclassid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID da turma de oferta relacionada (local_offermanager_class).',
            ],
            'ueid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID do registro de inscrição do usuário (user_enrolments).',
            ],
            'userid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID do usuário inscrito (usuário).',
            ],
            'courseid' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'description' => 'O ID do curso associado à inscrição (curso).',
            ],
            'situation' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'default' => constants::OFFER_USER_ENROL_SITUATION_ENROLED,
                'choices' => constants::get_situations(),
                'description' => 'A situação de matrícula (usa constantes de local_offermanager\\constants).',
            ],
            'progress' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_NOT_ALLOWED,
                'default' => 0,
                'description' => 'Progresso do usuário no curso (0-100%).',
            ],
            'grade' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'description' => 'Nota final do usuário no curso.',
            ],
            'extensioncount' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'default' => 0,
                'description' => 'Número de vezes que o prazo foi estendido para este usuário.',
            ],
            'self_canceled' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'choices' => [0, 1],
                'description' => 'Indica se o próprio usuário cancelou a inscrição (0 = não, 1 = sim).',
                'default' => null
            ],
            'usercanceled' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'description' => 'ID do usuário que realizou o cancelamento.',
                'default' => null
            ],
            'timecanceled' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'description' => 'Timestamp do momento em que o cancelamento ocorreu.',
            ],
            'history' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'default' => 0,
                'choices' => [0, 1],
                'description' => 'Indica se a matrícula foi movida para o histórico (0=não, 1=sim).',
            ],
            'userhistory' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'description' => 'ID do usuário que moveu a matrícula para o histórico.',
            ],
            'timehistory' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'description' => 'UNIX Timestamp quando a matrícula foi movida para o histórico.',
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'ID do usuário que criou o registro.',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;

                    return $USER->id;
                }
            ],
        ];
    }

    /**
     * Validates the extensioncount field.
     *
     * @param int $value The extension count to validate.
     * @return bool Returns true if valid.
     */
    protected function validate_extensioncount(int $value): bool
    {
        return $value >= 0;
    }

    /**
     * Validates the offerclassid field.
     *
     * @param int $value The offer class ID to validate.
     * @return bool Returns true if valid or a language string error message.
     */
    protected function validate_offerclassid(int $value)
    {
        if (!$value || !offer_class_model::record_exists($value)) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Validates the ueid field.
     *
     * @param int $value The user enrolment ID to validate.
     * @return bool Returns true if valid or a language string error message.
     */
    protected function validate_ueid(int $value)
    {
        global $DB;
        if (!$value || !$DB->record_exists('user_enrolments', ['id' => $value])) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Validates the userid field.
     *
     * @param int $value The user ID to validate.
     * @return bool Returns true if valid or a language string error message.
     */
    protected function validate_userid(int $value)
    {
        if (!$value || !\core_user::get_user($value)) {
            throw new moodle_exception('error:user_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Validates the courseid field.
     *
     * @param int $value The course ID to validate.
     * @return bool Returns true if valid or a language string error message.
     */
    protected function validate_courseid(int $value)
    {
        if (!$value || !get_course($value)) {
            throw new moodle_exception('error:course_not_found', 'local_offermanager');
        }
        return true;
    }

    /**
     * Returns the user enrolment instance from DB.
     *
     * @return void
     */
    public function fetch_user_enrolment(): void
    {
        global $DB;

        $table = $this->get_table();

        $this->user_enrolment = $DB->get_record(
            $table,
            [
                'id' => $this->get('ueid')
            ],
            '*'
        );
    }

    /**
     * Returns the user enrolment instance.
     *
     * @return stdClass|bool|null The user enrolment record.
     */
    public function get_user_enrolment(): stdClass|bool|null
    {
        if (!$this->user_enrolment) {
            $this->fetch_user_enrolment();
        }

        return $this->user_enrolment;
    }

    public function get_field_from_user_enrolment(string $field)
    {
        $user_enrolment = $this->get_user_enrolment();

        return $user_enrolment->{$field} ?? false;
    }

    public function has_started()
    {
        return $this->get_field_from_user_enrolment('timestart') < time();
    }

    public static function get_by_ueid(int $ueid): self
    {
        return self::get_record(
            [
                'ueid' => $ueid,
            ]
        );
    }

    public function fetch_offer_class(): offer_class_model|bool
    {
        return offer_class_model::get_record(
            [
                'id' => $this->get('offerclassid')
            ]
        );
    }

    public function get_offer_class(): offer_class_model|null
    {
        if (is_null($this->offerclass)) {
            $this->offerclass = $this->fetch_offer_class() ?: null;
        }
        return $this->offerclass;
    }

    public static function get_active_offer_user_enrol(int $userid, int $courseid): self|bool
    {
        $offer_user_enrols = self::get_records(
            [
                'userid' => $userid,
                'courseid' => $courseid,
            ]
        );

        foreach ($offer_user_enrols as $offer_user_enrol) {
            if ($offer_user_enrol->is_active()) {
                return $offer_user_enrol;
            }
        }

        return false;
    }

    /**
     * Edits the user enrolment details.
     *
     * @param int $status The new status of the enrolment.
     * @param int $timestart The new start time of the enrolment.
     * @param int $timeend The new end time of the enrolment.
     * @return mixed The result of the enrolment manager edit operation.
     * @throws moodle_exception If an error occurs during the edit.
     */
    public function edit_enrolment(
        $status,
        $timestart,
        $timeend
    ) {
        global $PAGE;

        $course = $this->get_course();
        $ue = $this->get_user_enrolment();

        $manager = new course_enrolment_manager($PAGE, $course);

        $data = new stdClass;

        $data->status = $status;
        $data->timestart = $timestart;
        $data->timeend = $timeend;

        $return = $manager->edit_enrolment($ue, $data);

        if ($return) {

            $olddata = new stdClass();
            $olddata->status = $ue->status;
            $olddata->timestart = $ue->status;
            $olddata->timeend = $ue->status;

            $this->fetch_user_enrolment();
            $ue = $this->get_user_enrolment();

            $newdata = new stdClass();
            $newdata->status = $ue->status;
            $newdata->timestart = $ue->timestart;
            $newdata->timeend = $ue->timeend;

            $event = offer_user_enrol_edited::instance($this, $olddata, $newdata);
            $event->trigger();
        }

        return $return;
    }

    /**
     * Método chamado antes da exclusão da instância do modelo.
     *
     * @return bool Retorna true se a operação deve continuar, false caso contrário.
     * @throws \moodle_exception
     */
    protected function before_delete(): bool
    {
        global $DB;

        $offerclass = $this->get_offer_class();
        $current_teachers = $offerclass->get_mapped_field('teachers');
        $userenrolment = $this->get_user_enrolment();


        if ($userenrolment) {
            $table = $this->get_table();
            if (!$DB->delete_records($table, ['id' => $userenrolment->id])) {
                throw new moodle_exception('error:delete_user_enrolment_fail', 'local_offermanager');
            }
        }


        if ($current_teachers) {
            $teacher_ids = explode(', ', $current_teachers);

            if (($key = array_search($this->get('userid'), $teacher_ids)) !== false) {
                unset($teacher_ids[$key]);
                $DB->set_field(
                    'enrol',
                    'customtext2',
                    implode(', ', $teacher_ids),
                    [
                        'id' => $offerclass->get('enrolid')
                    ]
                );
            }
        }

        manager::kill_user_sessions($this->get('userid'));

        $event = offer_user_enrol_deleted::instance($this);
        $event->trigger();

        return true;
    }

    /**
     * Método chamado após a criação da instância do modelo.
     */
    protected function after_create()
    {
        $event = offer_user_enrol_created::instance($this);
        $event->trigger();
    }

    protected function get_table()
    {
        return $this->get('history') ? offer_user_enrol_history_model::TABLE : 'user_enrolments';
    }
}
