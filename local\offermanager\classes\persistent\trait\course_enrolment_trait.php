<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\enrol_setup;

use local_offermanager\constants;
use local_offermanager\persistent\offer_user_enrol_model;


/**
 * Trait course_enrolment_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait course_enrolment_trait
{
    /**
     * Retorna os ids das instâncias de inscrição que estão associadas a instância.
     *
     * @return array
     */
    public function get_enrol_ids()
    {
        $offer_classes = $this->get_classes();

        return array_map(
            function ($offer_class) {
                return $offer_class->get('enrolid');
            },
            $offer_classes
        );
    }

    /**
     * Retorna as instâncias de inscrição que estão associadas a este registro.
     *
     * @return array
     */
    public function get_enrol_instances()
    {
        global $DB;

        $enrolids = $this->get_enrol_ids();

        if (!$enrolids) {
            return [];
        }

        [$insql, $inparams] = $DB->get_in_or_equal($enrolids);

        return $DB->get_records_select(
            'enrol',
            "id {$insql}",
            $inparams
        );
    }

    public function get_classes_with_disabled_enrol_instances(): array
    {
        $disabled_classes = [];
        $classes = $this->get_classes();

        if (!$classes) {
            return $disabled_classes;
        }

        foreach ($classes as $class) {
            if ($class->get_mapped_field('ue_status')) {
                $disabled_classes[] = $class;
            }
        }

        return $disabled_classes;
    }

    /**
     * Verifica se a instância possui instâncias de inscrição criadas.
     *
     * @return bool
     */
    public function has_enrol_instances()
    {
        return count($this->get_enrol_ids()) > 0;
    }

    /**
     * Retorna as inscrições associadas a instância.
     *
     * @return array
     */
    public function get_user_enrolments()
    {
        global $DB;

        $enrolids = $this->get_enrol_ids();

        if (!$enrolids) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($enrolids);

        return $DB->get_records_select(
            'user_enrolments',
            "enrolid {$insql}",
            $inparams
        );
    }

    /**
     * Verifica se existem inscrições associadas a instância.
     *
     * @return array
     */
    public function has_user_enrolments()
    {
        global $DB;

        $enrolids = $this->get_enrol_ids();

        if (!$enrolids) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($enrolids);

        return $DB->record_exists_select(
            'user_enrolments',
            "enrolid {$insql}",
            $inparams
        );
    }

    /**
     * Verifica se o usuário possui uma inscrição ativa no curso atual
     * considerando apenas os métodos offer_manual, offer_self e offer_automatic
     * 
     * @param int $userid ID do usuário a ser verificado
     * @param int|null $excluded_instance_id Enrol instance id para desconsiderar na validação
     * @return bool True se o usuário tem inscrição ativa, False caso contrário
     */
    public function has_active_user_enrolment(int $userid, int|null $excluded_instance_id = null)
    {
        global $DB;

        $course = $this->get_course();
        $now = time();

        if (empty($course) || empty($userid)) {
            return false;
        }

        $params = [];

        $params[] = $course->id;
        $params[] = $userid;
        $params[] = ENROL_USER_ACTIVE;
        $params[] = ENROL_INSTANCE_ENABLED;
        $params[] = $now;
        $params[] = $now;

        $offer_plugins = enrol_setup::get_dependent_enrol_plugins();

        if (!$offer_plugins) {
            return false;
        }

        $and = '';

        if ($excluded_instance_id) {
            $and .= 'AND ue.enrolid <> ?';
            $params[] = $excluded_instance_id;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($offer_plugins);

        $sql = "SELECT ue.id
            FROM {user_enrolments} ue
                JOIN {enrol} e ON e.id = ue.enrolid
            WHERE e.courseid = ?
                AND ue.userid = ?
                AND ue.status = ?
                AND e.status = ?
                AND (ue.timeend = 0 OR ue.timeend > ?)
                {$and}
                AND e.enrol {$insql}
        ";
        return $DB->record_exists_sql($sql, array_merge($params, $inparams));
    }

    /**
     * Retorna as inscrições do usuário no curso que ainda não iniciaram.
     *
     * @param int $userid ID do usuário.
     * @return array Array de objetos offer_user_enrol_model.
     */
    public function get_user_enrolments_to_start(int $userid): array
    {
        if (empty($userid)) {
            return [];
        }

        $enrolments = offer_user_enrol_model::get_offer_enrolments_by_user_and_course(
            $userid,
            $this->get('courseid'),
            constants::OFFER_USER_ENROL_SITUATION_ENROLED
        );
        if (empty($enrolments)) {
            return [];
        }

        $now = time();
        $enrolments_to_start = [];

        foreach ($enrolments as $enrolment) {
            $timestart = $enrolment->get_field_from_user_enrolment('timestart');
            if (!empty($timestart) && $timestart > $now) {
                $enrolments_to_start[] = $enrolment;
            }
        }

        return $enrolments_to_start;
    }

    /**
     * Cancela as inscrições do usuário no curso que ainda não iniciaram.
     *
     * @param int $userid ID do usuário cujas inscrições serão canceladas.
     * @param int $offerclassid ID da nova turma na qual o usuário foi inscrito (para a mensagem de motivo).
     * @return bool True se alguma inscrição foi cancelada, false caso contrário.
     */
    public function cancel_user_enrolments_to_start(int $userid, int $offerclassid): bool
    {
        global $USER;

        $enrolments_to_cancel = $this->get_user_enrolments_to_start($userid);

        if (empty($enrolments_to_cancel)) {
            return false;
        }

        $canceled_count = 0;
        foreach ($enrolments_to_cancel as $enrolment) {
            $reason = get_string(
                'message:reason_cancel_previous_enrolment',
                'local_offermanager',
                (object) [
                    'adminid' => $USER->id,
                    'userid' => $userid,
                    'newofferclassid' => $offerclassid
                ]
            );

            if ($enrolment->set_canceled($reason)) {
                $canceled_count++;
            }
        }

        return $canceled_count > 0;
    }

    public function enable_enrol_instances()
    {
        $classes = $this->get_classes_with_disabled_enrol_instances();

        if (!$classes) {
            return;
        }

        foreach ($classes as $class) {
            $class->enable_enrol_instance();
        }
    }
}
