<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\classes\external;


use local_offermanager\persistent\offer_model;
use local_offermanager\external\offer_class_external;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\trait\offer_enrol_plugin_trait;
use local_offermanager\enrol_setup;
use local_offermanager\constants;
use moodle_exception;
use context_course;
use core_external\external_api;
use enrol_offer_manual_plugin;
use invalid_parameter_exception;

/**
 * Tests for Gerenciados de Ofertas
 *
 * @package    local_offermanager
 * @category   test
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class offer_class_external_test extends \advanced_testcase
{
    const NUM_USERS = 10;

    protected $offer;
    protected $users;
    protected $course;
    protected $offercourse;
    protected $generator;
    protected array $class_methods;

    protected $fields_map;

    protected int $system_num_users = 0;

    protected function setUp(): void
    {
        global $DB;
        $this->resetAfterTest();
        $this->fields_map = enrol_setup::FIELDS_MAP;

        enrol_setup::disable_enrol_plugins();

        $this->generator = $this->getDataGenerator();

        $this->offer = new offer_model(0, (object)[
            'name' => 'Teste'
        ]);
        $this->offer->save();

        $this->course = $this->generator->create_course();

        $this->offercourse = $this->offer->add_course($this->course->id);
        if (!$this->offercourse || !$this->offercourse->get('id')) {
            throw new \RuntimeException('Falha ao criar offer_course durante o setUp do teste.');
        }

        $this->class_methods = enrol_setup::get_dependent_enrol_plugins();

        $this->system_num_users = $DB->count_records('user');

        for ($i = 0; $i < self::NUM_USERS; $i++) {

            $this->users[] = $this->generator->create_user();
        }
    }

    public function test_add_instance_success()
    {
        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate
        );

        $cleaned_result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $fields_map = $this->fields_map;

        $this->assertStringContainsString($classname, $cleaned_result);

        $instances = enrol_get_instances($this->course->id, true);
        $this->assertCount(1, $instances);
        $instance = reset($instances);
        $this->assertEquals($classname, $instance->{$fields_map['classname']});
        $this->assertEquals($startdate, $instance->{$fields_map['startdate']});
        $this->assertEquals('offer_manual', $instance->enrol);
    }

    public function test_add_instance_invalid_plugin()
    {
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:enrol_plugin_not_found', 'local_offermanager'));

        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        offer_class_external::add(
            'nonexistent',
            $this->offercourse->get('id'),
            $classname,
            $startdate
        );
    }

    public function test_add_instance_invalid_offercourse()
    {
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_course_not_found', 'local_offermanager'));

        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id') + 1000,
            $classname,
            $startdate
        );
    }

    public function test_add_instance_wrong_offerclassid()
    {
        $classname = 'Turma Teste 2025';
        $startdate = '2025-03-01';

        $this->expectException(invalid_parameter_exception::class);

        $result = offer_class_external::add(
            'offer_manual',
            'Eleven',
            $classname,
            $startdate
        );
    }

    public function test_add_instance_wrong_startdate()
    {
        $classname = 'Turma Teste 2025';
        $startdate = '2025-03-01';

        $this->expectException(invalid_parameter_exception::class);

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate
        );
    }

    public function test_add_instance_with_teachers()
    {
        $teachers = [
            $this->users[0]->id,
            $this->users[1]->id
        ];

        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate,
            $teachers
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $instances = enrol_get_instances($this->course->id, false);

        $instance = reset($instances);

        $fields_map = $this->fields_map;

        $this->assertEquals(get_string('message:class_created', 'local_offermanager', $instance), $result);
        $this->assertEquals(implode(', ', $teachers), $instance->{$fields_map['teachers']});
    }

    public function test_add_instance_with_custom_fields()
    {
        $fields = [
            'enableenddate' => 1,
            'enddate' => strtotime('2025-12-31'),
            'roleid' => 5,
            'enrolperiod' => 365 * DAYSECS,
            'minusers' => 5,
            'maxusers' => 30,
            'enableextension' => 1,
            'extensiondaysavailable' => 30,
            'extensionmaxrequests' => 2,
            'description' => 'Texto customizado',
            'extensionallowedsituations' => [constants::OFFER_USER_ENROL_SITUATION_ENROLED, constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS],
        ];

        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate,
            [],
            $fields
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $instances = enrol_get_instances($this->course->id, false);
        $instance = reset($instances);

        $fields_map = $this->fields_map;

        $this->assertEquals('Turma Teste 2025', $instance->{$fields_map['classname']});
        $this->assertEquals(strtotime('2025-03-01'), $instance->{$fields_map['startdate']});
        $this->assertEquals(strtotime('2025-12-31'), $instance->{$fields_map['enddate']});
        $this->assertEquals(5, $instance->{$fields_map['roleid']});
        $this->assertEquals(365 * DAYSECS, $instance->{$fields_map['enrolperiod']});

        $this->assertEquals(1, $instance->{$fields_map['enableenddate']});
        $this->assertEquals(5, $instance->{$fields_map['minusers']});
        $this->assertEquals(30, $instance->{$fields_map['maxusers']});
        $this->assertEquals(1, $instance->{$fields_map['enableextension']});

        $this->assertEquals('Texto customizado', $instance->{$fields_map['description']});
        // $this->assertEquals(30, $instance->{$fields_map['extensiondaysavailable']});
        // $this->assertEquals(2, $instance->{$fields_map['extensionmaxrequests']});
        //$this->assertEquals(implode(', ', ['status1', 'status2']), $instance->??);
    }

    public function test_add_instance_duplicate_classname()
    {

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            'Turma Duplicada',
            strtotime('2025-04-01')
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            'Turma Duplicada',
            strtotime('2025-04-01')
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $instances = array_values(enrol_get_instances($this->course->id, false));

        $fields_map = $this->fields_map;

        $this->assertCount(2, $instances);
        $this->assertEquals('Turma Duplicada', $instances[0]->{$fields_map['classname']});
        $this->assertEquals('Turma Duplicada', $instances[1]->{$fields_map['classname']});
    }

    public function test_enrol_users_success()
    {
        $offerclass = $this->create_test_offer_class();
        $user1 = $this->users[0];
        $user2 = $this->users[1];
        $roleid = 5;

        $result = offer_class_external::enrol_users(
            $offerclass->get('id'),
            [
                $user1->id,
                $user2->id
            ],
            $roleid
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::enrol_users_returns(),
            $result
        );

        $this->assertCount(2, $result);

        $this->assertEquals($user1->id, $result[0]['userid']);
        $this->assertEquals(get_string('message:enrolment_success', 'local_offermanager'), $result[0]['message']);
        $this->assertTrue($result[0]['success']);

        $this->assertEquals($user2->id, $result[1]['userid']);
        $this->assertTrue($result[1]['success']);

        $enrolled = get_enrolled_users(context_course::instance($this->course->id));
        $this->assertCount(2, $enrolled);
    }

    public function test_enrol_users_nonexistent_class()
    {
        $this->resetAfterTest(true);

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_not_found', 'local_offermanager'));

        offer_class_external::enrol_users(999, [$this->users[0]->id], 5);
    }

    public function test_enrol_users_nonexistent_user()
    {
        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $instances = enrol_get_instances($this->course->id, false);
        $instance = reset($instances);

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        $result = offer_class_external::enrol_users(
            $offerclass->get('id'),
            [9999],
            5
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::enrol_users_returns(),
            $result
        );

        $this->assertFalse($result[0]['success']);
        $this->assertStringContainsString(get_string('error:user_not_found', 'local_offermanager', 9999), $result[0]['message']);
    }

    public function test_enrol_users_already_enrolled()
    {
        $classname = 'Turma Teste 2025';
        $startdate = strtotime('2025-03-01');

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::add_returns(),
            $result
        );

        $instances = enrol_get_instances($this->course->id, false);
        $instance = reset($instances);

        $offerclass = offer_class_model::get_by_enrolid($instance->id);
        $user = $this->users[0];

        $result = offer_class_external::enrol_users(
            $offerclass->get('id'),
            [$user->id],
            5
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::enrol_users_returns(),
            $result
        );

        $this->assertTrue($result[0]['success']);

        $result = offer_class_external::enrol_users(
            $offerclass->get('id'),
            [$user->id],
            5
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::enrol_users_returns(),
            $result
        );

        $this->assertFalse($result[0]['success']);
        $this->assertEquals(get_string('error:user_already_enrolled', 'local_offermanager'), $result[0]['message']);
    }

    public function test_enrol_users_multiple_users_mixed_results()
    {
        $offerclass = $this->create_test_offer_class();

        $user1 = $this->users[0];
        $user2 = 9999;
        $user3 = $this->users[1];

        $result = offer_class_external::enrol_users(
            $offerclass->get('id'),
            [
                $user3->id
            ],
            5
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::enrol_users_returns(),
            $result
        );

        $result = offer_class_external::enrol_users(
            $offerclass->get('id'),
            [
                $user1->id,
                $user2,
                $user3->id
            ],
            5
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::enrol_users_returns(),
            $result
        );

        $this->assertCount(3, $result);
        $this->assertTrue($result[0]['success']);
        $this->assertFalse($result[1]['success']);
        $this->assertFalse($result[2]['success']);
    }

    public function test_get_potential_teachers_without_classid()
    {
        $this->resetAfterTest(true);

        global $DB;

        $result = offer_class_external::get_potential_teachers(0);

        $result = external_api::clean_returnvalue(
            offer_class_external::get_potential_teachers_returns(),
            $result
        );

        $expected_count = $DB->count_records('user', ['deleted' => 0, 'confirmed' => 1]);

        $this->assertCount($expected_count, $result);
    }

    public function test_get_potential_teachers_with_classid()
    {

        $this->resetAfterTest(true);

        $offer = new offer_model(0, (object) [
            'name' => 'Oferta Teste'
        ]);
        $offer->save();
        $course = $this->getDataGenerator()->create_course();
        $offer_course = $offer->add_course($course->id);

        $add_result = offer_class_external::add(
            'offer_manual',
            $offer_course->get('id'),
            'Turma A',
            time(),
            [$this->users[9]->id]
        );
        external_api::clean_returnvalue(offer_class_external::add_returns(), $add_result);

        $instances = enrol_get_instances($course->id, false);
        $instance = reset($instances);
        $offerclass = offer_class_model::get_by_enrolid($instance->id);
        $teachers = $offerclass->get_teachers();
        $teacher_userid = (int) $this->users[9]->id;

        $this->assertTrue(in_array($teacher_userid, $teachers));

        $result = offer_class_external::get_potential_teachers($offerclass->get('id'));

        $result = external_api::clean_returnvalue(
            offer_class_external::get_potential_teachers_returns(),
            $result
        );

        $userids = array_column($result, 'id');

        $this->assertContains($teacher_userid, $userids);
        $this->assertCount($this->system_num_users + self::NUM_USERS - 1, $result);

        // $this->assertNotContains($teacher_userid, $userids);
        // $expected_count = $DB->count_records('user', ['deleted' => 0, 'confirmed' => 1]) - 1; // Todos menos o professor
        // $this->assertCount($expected_count, $result);

    }

    public function test_get_potential_teachers_excludes_deleted_unconfirmed()
    {
        $active_user = $this->users[0];

        $deleted_user = $this->generator->create_user(['deleted' => 1]);
        $unconfirmed_user = $this->generator->create_user(['confirmed' => 0]);

        $result = offer_class_external::get_potential_teachers(0);

        $result = external_api::clean_returnvalue(
            offer_class_external::get_potential_teachers_returns(),
            $result
        );

        $userids = array_column($result, 'id');

        $this->assertContains((int) $active_user->id, $userids);
        $this->assertNotContains((int) $deleted_user->id, $userids);
        $this->assertNotContains((int) $unconfirmed_user->id, $userids);
    }

    public function test_get_potential_teachers_return_structure()
    {
        $this->generator->create_user();

        $result = offer_class_external::get_potential_teachers(0);

        $result = external_api::clean_returnvalue(
            offer_class_external::get_potential_teachers_returns(),
            $result
        );

        $this->assertNotEmpty($result, 'get_potential_teachers retornou um array vazio.');
        $user = reset($result);
        $this->assertArrayHasKey('id', $user);
        $this->assertArrayHasKey('fullname', $user);
        $this->assertIsInt($user['id']);
        $this->assertIsString($user['fullname']);
    }

    public function test_update_instance_success()
    {
        $offerclass = $this->create_test_offer_class('Turma Original', strtotime('2025-01-01'));

        $new_classname = 'Turma Atualizada';
        $new_startdate = strtotime('2025-02-01');

        $result = offer_class_external::update(
            $offerclass->get('id'),
            $new_classname,
            $new_startdate,
            [],
            []
        );

        $cleaned_result = external_api::clean_returnvalue(
            offer_class_external::update_returns(),
            $result
        );

        $offerclass->fetch_enrol_instance();

        $enrol_instance = $offerclass->get_enrol_instance();

        $this->assertStringContainsString(
            get_string('message:class_updated', 'local_offermanager', $enrol_instance),
            $cleaned_result
        );

        $fields_map = $this->fields_map;

        $this->assertEquals($new_classname, $enrol_instance->{$fields_map['classname']});
        $this->assertEquals($new_startdate, $enrol_instance->{$fields_map['startdate']});
        $this->assertEquals('offer_manual', $enrol_instance->enrol);
    }

    public function test_update_instance_with_optional_fields()
    {
        $offerclass = $this->create_test_offer_class();

        $new_fields = [
            'enableenddate' => 1,
            'enddate' => strtotime('2026-12-31'),
            'roleid' => 3,
            'enrolperiod' => 180 * DAYSECS,
            'minusers' => 10,
            'maxusers' => 50,
            'enableextension' => 0,
            'extensiondaysavailable' => 15,
            'extensionmaxrequests' => 1,
            'description' => 'Nova Descrição',
            'extensionallowedsituations' => [constants::OFFER_USER_ENROL_SITUATION_APPROVED]
        ];

        $starttime =  time() + 3600;
        $result = offer_class_external::update(
            $offerclass->get('id'),
            'Turma com Opcionais Atualizada',
            $starttime,
            [],
            $new_fields
        );

        $result = external_api::clean_returnvalue(offer_class_external::update_returns(), $result);

        $offerclass->fetch_enrol_instance();
        $instance = $offerclass->get_enrol_instance();

        $fields_map = $this->fields_map;

        $this->assertEquals(get_string('message:class_updated', 'local_offermanager', $instance), $result);
        $this->assertEquals('Turma com Opcionais Atualizada', $instance->{$fields_map['classname']});
        $this->assertEquals($starttime, $instance->{$fields_map['startdate']});
        $this->assertEquals(3, $instance->{$fields_map['roleid']});
        $this->assertEquals(180 * DAYSECS, $instance->{$fields_map['enrolperiod']});

        $this->assertEquals(1, $instance->{$fields_map['enableenddate']});
        $this->assertEquals(10, $instance->{$fields_map['minusers']});
        $this->assertEquals(50, $instance->{$fields_map['maxusers']});
        $this->assertEquals(0, $instance->{$fields_map['enableextension']});

        $this->assertEquals('Nova Descrição', $instance->{$fields_map['description']});
        //$this->assertEquals(15, ??);
        //$this->assertEquals(1, ??);
        //$this->assertEquals(json_encode(['approved']), ??);
    }

    public function test_update_instance_with_teachers()
    {
        $initial_teachers = [$this->users[0]->id];
        $offerclass = $this->create_test_offer_class('Turma Professores', time(), $initial_teachers);

        $new_teachers = [
            $this->users[1]->id,
            $this->users[2]->id
        ];

        $result = offer_class_external::update(
            $offerclass->get('id'),
            'Turma Professores Atualizada',
            time() + 7200,
            $new_teachers,
            []
        );

        external_api::clean_returnvalue(offer_class_external::update_returns(), $result);

        $instance = $offerclass->get_enrol_instance(true);
        $this->assertEquals(get_string('message:class_updated', 'local_offermanager', $instance), $result);

        $fields_map = $this->fields_map;

        $this->assertEquals(implode(', ', $new_teachers), $instance->{$fields_map['teachers']});
    }

    public function test_update_instance_nonexistent_class()
    {
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_not_found', 'local_offermanager'));

        offer_class_external::update(
            9999, // ID inexistente
            'Nome Qualquer',
            time(),
            [],
            []
        );
    }

    public function test_update_instance_invalid_parameters()
    {
        $offerclass = $this->create_test_offer_class();

        $this->expectException(invalid_parameter_exception::class);

        offer_class_external::update(
            $offerclass->get('id'),
            'offer_manual',
            'Nome Qualquer',
            'data invalida',
            [],
            []
        );
    }

    /**
     * Cria uma instância de turma para testes de atualização e exclusão.
     *
     * @param string $classname
     * @param int $startdate
     * @param array $teachers
     * @param array $optional_fields
     * @return offer_class_model
     */
    protected function create_test_offer_class(
        string $classname = 'Turma Original',
        int $startdate = 0,
        array $teachers = [],
        array $optional_fields = []
    ): offer_class_model {
        if ($startdate === 0) {
            $startdate = time();
        }

        $result = offer_class_external::add(
            'offer_manual',
            $this->offercourse->get('id'),
            $classname,
            $startdate,
            $teachers,
            $optional_fields
        );

        external_api::clean_returnvalue(offer_class_external::add_returns(), $result);

        $instances = enrol_get_instances($this->course->id, false);
        $instance = reset($instances);
        return offer_class_model::get_by_enrolid($instance->id);
    }

    protected function total_users()
    {
        global $DB;
        return $DB->count_records('user', ['deleted' => 0]);
    }

    public function test_get_success()
    {
        $offerclass = $this->create_test_offer_class();

        $result = offer_class_external::get($offerclass->get('id'));

        $result = external_api::clean_returnvalue(
            offer_class_external::get_returns(),
            $result
        );

        $fields_map = $this->fields_map;

        $enrol_instance = $offerclass->get_enrol_instance();

        $this->assertEquals($enrol_instance->enrol, $result['enrol']);
        $this->assertEquals($offerclass->get('offercourseid'), $result['offercourseid']);
        $this->assertEquals($enrol_instance->{$fields_map['classname']}, $result['classname']);
        $this->assertEquals($enrol_instance->{$fields_map['startdate']}, $result['startdate']);
        $this->assertEquals($offerclass->get_teachers(), $result['teachers']);

        $this->assertArrayHasKey('enableenddate', $result['optional_fields']);
        $this->assertArrayHasKey('enddate', $result['optional_fields']);
        $this->assertArrayHasKey('enablepreenrolment', $result['optional_fields']);
        $this->assertArrayHasKey('preenrolmentstartdate', $result['optional_fields']);
        $this->assertArrayHasKey('preenrolmentenddate', $result['optional_fields']);
        $this->assertArrayHasKey('description', $result['optional_fields']);
        $this->assertArrayHasKey('enableenrolperiod', $result['optional_fields']);
        $this->assertArrayHasKey('enrolperiod', $result['optional_fields']);
        $this->assertArrayHasKey('minusers', $result['optional_fields']);
        $this->assertArrayHasKey('maxusers', $result['optional_fields']);
        $this->assertArrayHasKey('roleid', $result['optional_fields']);
        $this->assertArrayHasKey('enablereenrol', $result['optional_fields']);
        $this->assertArrayHasKey('reenrolmentsituations', $result['optional_fields']);
        $this->assertArrayHasKey('enableextension', $result['optional_fields']);
        $this->assertArrayHasKey('extensionperiod', $result['optional_fields']);
        $this->assertArrayHasKey('extensiondaysavailable', $result['optional_fields']);
        $this->assertArrayHasKey('extensionmaxrequests', $result['optional_fields']);
        $this->assertArrayHasKey('extensionallowedsituations', $result['optional_fields']);
    }

    public function test_get_nonexistent_class()
    {
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_not_found', 'local_offermanager'));

        offer_class_external::get(9999);
    }

    public function test_delete_success()
    {
        $offerclass = $this->create_test_offer_class();
        $offerclassid = $offerclass->get('id');
        $enrol_instance = $offerclass->get_enrol_instance();
        $result = offer_class_external::delete($offerclassid);

        $cleaned_result = external_api::clean_returnvalue(
            offer_class_external::delete_returns(),
            $result
        );

        $this->assertStringContainsString(get_string('message:class_deleted', 'local_offermanager', $enrol_instance), $cleaned_result);

        $instances = enrol_get_instances($this->course->id, false);
        foreach ($instances as $instance) {
            $this->assertNotEquals($offerclassid, $instance->id);
        }
    }

    public function test_delete_nonexistent_class()
    {
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_not_found', 'local_offermanager'));

        offer_class_external::delete(9999);
    }

    public function test_delete_invalid_plugin()
    {
        global $DB;
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:enrol_plugin_not_found', 'local_offermanager'));

        $offerclass = $this->create_test_offer_class();
        $instance = $offerclass->get_enrol_instance();
        $instance->enrol = 'invalidplugin';
        $DB->update_record('enrol', $instance);

        offer_class_external::delete($offerclass->get('id'));
    }

    public function test_duplicate_success()
    {
        $original_classname = 'Turma Original para Duplicar';
        $original_startdate = time();
        $original_teachers = [$this->users[0]->id, $this->users[1]->id];
        $original_offerclass = $this->create_test_offer_class($original_classname, $original_startdate, $original_teachers);
        $original_offerclassid = $original_offerclass->get('id');
        $original_enrol_instance = $original_offerclass->get_enrol_instance();

        $target_course = $this->generator->create_course();
        $target_offercourse = $this->offer->add_course($target_course->id);
        $target_offercourseid = $target_offercourse->get('id');

        $result = offer_class_external::duplicate($original_offerclassid, $target_offercourseid);

        $cleaned_result = external_api::clean_returnvalue(
            offer_class_external::duplicate_returns(),
            $result
        );

        $this->assertIsArray($cleaned_result);
        $this->assertArrayHasKey('id', $cleaned_result);
        $new_offerclassid = $cleaned_result['id'];
        $this->assertNotEquals($original_offerclassid, $new_offerclassid);

        $new_offerclass = offer_class_model::get_record(['id' => $new_offerclassid]);
        $this->assertInstanceOf(offer_class_model::class, $new_offerclass);
        $new_enrol_instance = $new_offerclass->get_enrol_instance();

        $fields_map = $this->fields_map;

        $this->assertEquals($target_offercourseid, $new_offerclass->get('offercourseid'));
        $this->assertEquals($original_classname, $new_enrol_instance->{$fields_map['classname']});
        $this->assertEquals($original_startdate, $new_enrol_instance->{$fields_map['startdate']});
        $this->assertEquals($original_offerclassid, $new_offerclass->get('clone_id'));

        $new_teachers = $new_offerclass->get_teachers();
        sort($original_teachers);
        sort($new_teachers);
        $this->assertEquals($original_teachers, $new_teachers);

        $this->assertEquals($new_offerclass->get('offercourseid'), $cleaned_result['offercourseid']);
        $this->assertEquals($new_enrol_instance->{$fields_map['classname']}, $cleaned_result['classname']);
        $this->assertEquals($new_enrol_instance->{$fields_map['startdate']}, $cleaned_result['startdate']);
        $this->assertEquals($new_teachers, $cleaned_result['teachers']); // Comparar professores no retorno

        $target_instances = enrol_get_instances($target_course->id, true);
        $this->assertCount(1, $target_instances);
        $new_instance_check = reset($target_instances);

        $fields_map = $this->fields_map;

        $this->assertEquals($new_enrol_instance->id, $new_instance_check->id);
        $this->assertEquals($original_classname, $new_instance_check->{$fields_map['classname']});

        $original_instances = enrol_get_instances($this->course->id, true);
        $found_original = false;
        foreach ($original_instances as $instance) {
            if ($instance->id === $original_enrol_instance->id) {
                $found_original = true;
                break;
            }
        }
        $this->assertTrue($found_original, 'Instância original não encontrada após duplicação.');
    }

    public function test_duplicate_different_offer()
    {
        $original_offerclass = $this->create_test_offer_class();
        $original_offerclassid = $original_offerclass->get('id');

        $another_offer = new offer_model(0, (object)['name' => 'Outra Oferta']);
        $another_offer->save();
        $target_course = $this->generator->create_course();
        $target_offercourse = $another_offer->add_course($target_course->id);
        $target_offercourseid = $target_offercourse->get('id');

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_different_offer', 'local_offermanager'));

        offer_class_external::duplicate($original_offerclassid, $target_offercourseid);
    }

    public function test_duplicate_same_course()
    {
        $original_offerclass = $this->create_test_offer_class();
        $original_offerclassid = $original_offerclass->get('id');
        $original_offercourseid = $original_offerclass->get('offercourseid');

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:duplicate_same_course', 'local_offermanager'));

        offer_class_external::duplicate($original_offerclassid, $original_offercourseid);
    }

    public function test_duplicate_nonexistent_original_class()
    {
        $target_course = $this->generator->create_course();
        $target_offercourse = $this->offer->add_course($target_course->id);
        $target_offercourseid = $target_offercourse->get('id');

        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_class_not_found', 'local_offermanager'));

        offer_class_external::duplicate(9999, $target_offercourseid);
    }

    public function test_duplicate_nonexistent_target_course()
    {
        // 1. Criar turma original
        $original_offerclass = $this->create_test_offer_class();
        $original_offerclassid = $original_offerclass->get('id');

        // 2. Esperar exceção
        $this->expectException(moodle_exception::class);
        $this->expectExceptionMessage(get_string('error:offer_course_not_found', 'local_offermanager'));

        // 3. Tentar duplicar para offercourseid inexistente
        offer_class_external::duplicate($original_offerclassid, 9999);
    }
    public function test_get_potential_duplication_courses()
    {
        $this->resetAfterTest(true);

        $course1 = $this->getDataGenerator()->create_course(['fullname' => 'Curso A']);
        $course2 = $this->getDataGenerator()->create_course(['fullname' => 'Curso B']);
        $course3 = $this->getDataGenerator()->create_course(['fullname' => 'Curso C']);

        $offercourse1 = $this->offer->add_course($course1->id);
        $offercourse2 = $this->offer->add_course($course2->id);
        $offercourse3 = $this->offer->add_course($course3->id);

        $plugin = new \enrol_offer_manual_plugin();

        $enrolid1 = $plugin->add_instance($course1, [
            'offercourseid' => $offercourse1->get('id'),
            'classname' => 'Turma 1',
            'startdate' => time(),
        ]);

        $offerclass = offer_class_model::get_by_enrolid($enrolid1);

        $result = offer_class_external::get_potential_duplication_courses(
            $offerclass->get('id')
        );

        $result = external_api::clean_returnvalue(
            offer_class_external::get_potential_duplication_courses_returns(),
            $result
        );

        $courseids = array_map(fn($c) => (int) $c['id'], $result);
        $this->assertNotContains((int) $course1->id, $courseids);
        $this->assertContains((int) $course2->id, $courseids);
        $this->assertContains((int) $course3->id, $courseids);
        $this->assertCount(3, $result);
    }
}
