<template>
  <div class="filter-row" :class="{ 'filter-row-inline': inline }">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'FilterRow',

  props: {
    inline: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-row {
  display: flex;
  align-items: flex-end;
  flex-wrap: nowrap;
  gap: 12px;
  margin-bottom: 1rem;

  &.filter-row-inline {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-end;
  }

  /* Garante que os componentes dentro do FilterRow tenham o espaçamento adequado */
  > * {
    margin-right: 8px;
  }

  /* Garante que o último componente não tenha margem à direita */
  > *:last-child {
    margin-right: 0;
  }

  @media (max-width: 768px) {
    &, &.filter-row-inline {
      flex-direction: column;
    }

    > * {
      margin-right: 0;
      margin-bottom: 8px;
    }

    > *:last-child {
      margin-bottom: 0;
    }
  }
}
</style>