<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\form;

use core_form\dynamic_form;
use local_offermanager\persistent\offer_user_enrol_model;
use stdClass;
use context_user;
use context;
use moodle_url;
use dml_exception;
use moodle_exception;
use context_offer_class;

defined('MOODLE_INTERNAL') || die();

/**
 * Class self_unenrol_form
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class self_unenrol_form extends dynamic_form{
       /**
     * Define form elements.
     *
     * @return void
     * @throws coding_exception
     * @throws dml_exception
     */
    protected function definition(): void
    {
        $mform = $this->_form;

        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        $mform->addElement('hidden', 'userid');
        $mform->setType('userid', PARAM_INT);

        $mform->addElement('static', 'confirm_message', '', get_string('message:self_unenrol', 'local_offermanager'));

        $mform->addElement('editor', 'reason', get_string('message:cancel_reason', 'local_offermanager'), ['rows' => 5, 'cols' => 30]);
        $mform->setType('reason', PARAM_TEXT);
        $mform->addHelpButton('reason', 'message:cancel_reason', 'local_offermanager');
    }

    /**
     * Get the context for dynamic submission.
     *
     * @return context The context.
     */
    public function get_context_for_dynamic_submission(): context
    {
        global $USER;
        return context_user::instance($USER->id);
    }

    /**
     * Check access for dynamic submission.
     *
     * @return void 
     * @throws dml_exception
     */
    protected function check_access_for_dynamic_submission(): void
    {
        global $USER;

        if (!isloggedin() || isguestuser()) {
            return;
        }

        if ($USER->id) {
            return;
        }

        $offeruserenrol = offer_user_enrol_model::get_record(
            [
                'id' => $this->optional_param('id', 0, PARAM_INT),
                'userid' => $USER->id
            ]
        );

        if (!$offeruserenrol || !$offeruserenrol->is_active()) {
            return;
        }

        $pluginname = $offeruserenrol->get('enrol');

        $offerclass = $offeruserenrol->get_offer_class();
        $offerclass_context = context_offer_class::instance($offerclass->get('id'));

        require_capability("enrol/{$pluginname}:unenrolself", $offerclass_context);
    }

    /**
     * Load in existing data as form defaults
     */
    public function set_data_for_dynamic_submission(): void
    {
        global $USER;

        $fields = new stdClass;
        $fields->id = $this->optional_param('id', 0, PARAM_INT);
        $fields->userid = $USER->id;
        $this->set_data($fields);
    }

    /**
     * Get the page URL for dynamic submission.
     *
     * @return moodle_url The page URL.
     */
    protected function get_page_url_for_dynamic_submission(): moodle_url
    {
        return new moodle_url('/my/');
    }

    /**
     * Process dynamic form submission.
     *
     * @return void
     * @throws \moodle_exception
     * @throws \dml_exception
     */
    public function process_dynamic_submission()
    {
        global $USER;

        $data = $this->get_data();
        $userid = (int) $data->userid;
        $offeruserenrolid = $data->id;
        $reason = $data->reason['text'] ?? null;

        if (!$offeruserenrolid) {
            throw new moodle_exception('error:formdata_missing', 'local_offermanager');
        }

        if (!$userid || $userid != $USER->id) {
            throw new moodle_exception('error:formdata_missing', 'local_offermanager');
        }

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $offeruserenrolid, 'userid' => $userid]);

        if (!$offer_user_enrol || !$offer_user_enrol->is_active()) {
            throw new moodle_exception('error:invalid_offer_user_enrol_for_cancel', 'local_offermanager');
        }
        $success = $offer_user_enrol->set_canceled($reason);

        return ['result' => $success];
    }

}
